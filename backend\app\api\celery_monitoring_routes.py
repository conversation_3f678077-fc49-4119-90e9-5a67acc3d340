"""
Celery监控API路由
提供Celery任务、Worker、队列的实时监控功能
"""

import logging
import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from app.celery_app import celery_app
from app.core.redis_client import get_redis_client

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/celery", tags=["Celery监控"])

# 缓存配置
CACHE_TTL = 10  # 缓存10秒
INSPECT_TIMEOUT = 2  # Celery inspect超时2秒
FALLBACK_CACHE_TTL = 60  # 降级缓存60秒


async def safe_inspect_with_timeout(inspect_func, timeout=INSPECT_TIMEOUT):
    """带超时的安全inspect调用"""
    try:
        # 使用asyncio.wait_for添加超时
        result = await asyncio.wait_for(
            asyncio.get_event_loop().run_in_executor(None, inspect_func),
            timeout=timeout
        )
        return result
    except asyncio.TimeoutError:
        logger.warning(f"Celery inspect operation timed out after {timeout}s")
        return None
    except Exception as e:
        logger.error(f"Celery inspect operation failed: {e}")
        return None


async def get_cached_data(redis_client, cache_key: str):
    """获取缓存数据"""
    try:
        cached = await redis_client.get(cache_key)
        if cached:
            return json.loads(cached)
    except Exception as e:
        logger.warning(f"Failed to get cached data: {e}")
    return None


async def set_cached_data(redis_client, cache_key: str, data: Any, ttl: int = CACHE_TTL):
    """设置缓存数据"""
    try:
        await redis_client.setex(cache_key, ttl, json.dumps(data, default=str))
        # 同时设置降级缓存，TTL更长
        fallback_key = f"{cache_key}:fallback"
        await redis_client.setex(fallback_key, FALLBACK_CACHE_TTL, json.dumps(data, default=str))
    except Exception as e:
        logger.warning(f"Failed to set cached data: {e}")


async def get_fallback_data(redis_client, cache_key: str):
    """获取降级缓存数据"""
    try:
        fallback_key = f"{cache_key}:fallback"
        cached = await redis_client.get(fallback_key)
        if cached:
            return json.loads(cached)
    except Exception as e:
        logger.warning(f"Failed to get fallback data: {e}")
    return None


# 响应模型
class CeleryTaskInfo(BaseModel):
    id: str
    name: str
    state: str
    args: List[Any]
    kwargs: Dict[str, Any]
    result: Optional[Any] = None
    traceback: Optional[str] = None
    timestamp: str
    runtime: Optional[float] = None
    worker: Optional[str] = None
    queue: Optional[str] = None
    retries: Optional[int] = None
    eta: Optional[str] = None


class CeleryWorkerInfo(BaseModel):
    name: str
    status: str
    active_tasks: int
    processed_tasks: int
    load_avg: List[float]
    pool_size: int
    pool_writes: int
    clock: int


class QueueInfo(BaseModel):
    name: str
    length: int
    consumers: int
    messages: int
    memory: int


class CeleryStats(BaseModel):
    total_tasks: int
    active_tasks: int
    pending_tasks: int
    success_tasks: int
    failed_tasks: int
    workers_online: int
    total_queues: int


@router.get("/stats", response_model=CeleryStats)
async def get_celery_stats():
    """获取Celery统计信息"""
    try:
        redis_client = await get_redis_client()
        cache_key = "celery:cache:stats"

        # 尝试从缓存获取
        cached_stats = await get_cached_data(redis_client, cache_key)
        if cached_stats:
            return CeleryStats(**cached_stats)

        inspect = celery_app.control.inspect()

        # 使用超时机制获取基本统计
        stats = await safe_inspect_with_timeout(inspect.stats)
        active_tasks = await safe_inspect_with_timeout(inspect.active)
        scheduled_tasks = await safe_inspect_with_timeout(inspect.scheduled)

        # 如果inspect失败，尝试使用降级缓存
        if stats is None and active_tasks is None and scheduled_tasks is None:
            fallback_stats = await get_fallback_data(redis_client, cache_key)
            if fallback_stats:
                logger.info("Using fallback cache for Celery stats")
                return CeleryStats(**fallback_stats)

        # 计算统计数据
        workers_online = len(stats or {})
        total_active = sum(len(tasks) for tasks in (active_tasks or {}).values())
        total_scheduled = sum(len(tasks) for tasks in (scheduled_tasks or {}).values())

        # 从Redis获取历史统计
        total_tasks = await redis_client.get("celery:stats:total_tasks") or 0
        success_tasks = await redis_client.get("celery:stats:success_tasks") or 0
        failed_tasks = await redis_client.get("celery:stats:failed_tasks") or 0

        result = {
            "total_tasks": int(total_tasks),
            "active_tasks": total_active,
            "pending_tasks": total_scheduled,
            "success_tasks": int(success_tasks),
            "failed_tasks": int(failed_tasks),
            "workers_online": workers_online,
            "total_queues": 1  # 默认队列数量
        }

        # 缓存结果
        await set_cached_data(redis_client, cache_key, result)

        return CeleryStats(**result)

    except Exception as e:
        logger.error(f"Failed to get Celery stats: {e}")
        # 返回默认值而不是抛出异常
        return CeleryStats(
            total_tasks=0,
            active_tasks=0,
            pending_tasks=0,
            success_tasks=0,
            failed_tasks=0,
            workers_online=0,
            total_queues=0
        )


@router.get("/stats/fast", response_model=CeleryStats)
async def get_celery_stats_fast():
    """获取Celery统计信息 - 快速版本，只返回基本信息"""
    try:
        redis_client = await get_redis_client()

        # 只从Redis获取基本统计，不调用inspect
        total_tasks = await redis_client.get("celery:stats:total_tasks") or 0
        success_tasks = await redis_client.get("celery:stats:success_tasks") or 0
        failed_tasks = await redis_client.get("celery:stats:failed_tasks") or 0

        # 检查队列长度作为pending_tasks的估算
        queue_length = await redis_client.llen("celery")

        return CeleryStats(
            total_tasks=int(total_tasks),
            active_tasks=0,  # 快速版本不检查活跃任务
            pending_tasks=queue_length,
            success_tasks=int(success_tasks),
            failed_tasks=int(failed_tasks),
            workers_online=1,  # 假设有一个worker在线
            total_queues=1
        )

    except Exception as e:
        logger.error(f"Failed to get fast Celery stats: {e}")
        return CeleryStats(
            total_tasks=0,
            active_tasks=0,
            pending_tasks=0,
            success_tasks=0,
            failed_tasks=0,
            workers_online=0,
            total_queues=0
        )


def _process_task_data(task: Dict, worker: str, state: str) -> Dict:
    """处理单个任务数据"""
    task_id = task.get('id', task.get('uuid', ''))
    task_name = task.get('name', task.get('type', ''))

    # 简化时间戳处理
    timestamp = task.get('time_start') or task.get('timestamp') or task.get('eta')
    if isinstance(timestamp, (int, float)):
        timestamp = datetime.fromtimestamp(timestamp).isoformat()
    elif not timestamp:
        timestamp = datetime.now().isoformat()

    # 简化队列信息处理
    delivery_info = task.get('delivery_info', {})
    queue = delivery_info.get('routing_key', 'default') if isinstance(delivery_info, dict) else 'default'

    return {
        "id": task_id,
        "name": task_name,
        "state": state,
        "args": task.get('args', []),
        "kwargs": task.get('kwargs', {}),
        "timestamp": timestamp,
        "worker": worker,
        "queue": queue,
        "eta": task.get('eta') if state == 'PENDING' else None
    }


@router.get("/tasks/active", response_model=List[CeleryTaskInfo])
async def get_active_tasks():
    """获取活跃任务列表"""
    try:
        redis_client = await get_redis_client()
        cache_key = "celery:cache:active_tasks"

        # 尝试从缓存获取
        cached_tasks = await get_cached_data(redis_client, cache_key)
        if cached_tasks:
            return [CeleryTaskInfo(**task) for task in cached_tasks]

        inspect = celery_app.control.inspect()

        # 使用超时机制获取任务
        active_tasks = await safe_inspect_with_timeout(inspect.active)
        scheduled_tasks = await safe_inspect_with_timeout(inspect.scheduled)

        tasks = []

        # 处理活跃任务
        if active_tasks:
            for worker, worker_tasks in active_tasks.items():
                for task in worker_tasks:
                    task_data = _process_task_data(task, worker, 'STARTED')
                    tasks.append(task_data)

        # 处理计划任务
        if scheduled_tasks:
            for worker, worker_tasks in scheduled_tasks.items():
                for task in worker_tasks:
                    task_data = _process_task_data(task, worker, 'PENDING')
                    tasks.append(task_data)

        # 缓存结果
        await set_cached_data(redis_client, cache_key, tasks)

        return [CeleryTaskInfo(**task) for task in tasks]

    except Exception as e:
        logger.error(f"Failed to get active tasks: {e}")
        # 返回空列表而不是抛出异常
        return []


@router.get("/tasks/history", response_model=List[CeleryTaskInfo])
async def get_task_history(
    limit: int = Query(100, description="返回任务数量限制"),
    state: Optional[str] = Query(None, description="任务状态筛选"),
    worker: Optional[str] = Query(None, description="Worker筛选")
):
    """获取任务历史记录"""
    try:
        redis_client = await get_redis_client()
        
        # 从Redis获取任务历史
        task_keys = await redis_client.keys("celery:task:*")
        tasks = []
        
        for key in task_keys[-limit:]:  # 限制数量
            task_data = await redis_client.hgetall(key)
            if task_data:
                # 应用筛选条件
                if state and task_data.get('state') != state:
                    continue
                if worker and task_data.get('worker') != worker:
                    continue
                
                tasks.append(CeleryTaskInfo(
                    id=task_data.get('id', ''),
                    name=task_data.get('name', ''),
                    state=task_data.get('state', 'UNKNOWN'),
                    args=eval(task_data.get('args', '[]')),
                    kwargs=eval(task_data.get('kwargs', '{}')),
                    result=task_data.get('result'),
                    traceback=task_data.get('traceback'),
                    timestamp=task_data.get('timestamp', ''),
                    runtime=float(task_data.get('runtime', 0)) if task_data.get('runtime') else None,
                    worker=task_data.get('worker'),
                    queue=task_data.get('queue'),
                    retries=int(task_data.get('retries', 0)) if task_data.get('retries') else None
                ))
        
        # 按时间戳排序
        tasks.sort(key=lambda x: x.timestamp, reverse=True)
        
        return tasks
        
    except Exception as e:
        logger.error(f"Failed to get task history: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/workers", response_model=List[CeleryWorkerInfo])
async def get_workers():
    """获取Worker状态"""
    try:
        redis_client = await get_redis_client()
        cache_key = "celery:cache:workers"

        # 尝试从缓存获取
        cached_workers = await get_cached_data(redis_client, cache_key)
        if cached_workers:
            return [CeleryWorkerInfo(**worker) for worker in cached_workers]

        inspect = celery_app.control.inspect()

        # 使用超时机制获取Worker统计信息
        stats = await safe_inspect_with_timeout(inspect.stats)
        active_tasks = await safe_inspect_with_timeout(inspect.active)

        workers = []
        
        worker_data = []
        if stats:
            for worker_name, worker_stats in stats.items():
                # 简化数据处理
                worker_active_tasks = len((active_tasks or {}).get(worker_name, []))

                # 简化统计数据获取
                total_stats = worker_stats.get('total', {})
                processed_tasks = sum(v for k, v in total_stats.items()
                                    if isinstance(v, int) and 'crawl' in k)

                # 简化load_avg处理
                rusage_stats = worker_stats.get('rusage', {})
                load_avg = rusage_stats.get('load_avg', [0.0, 0.0, 0.0])
                if not isinstance(load_avg, list) or len(load_avg) < 3:
                    load_avg = [0.0, 0.0, 0.0]

                # 简化pool数据处理
                pool_stats = worker_stats.get('pool', {})
                pool_size = int(pool_stats.get('max-concurrency', 4))
                pool_writes = int(pool_stats.get('writes', {}).get('total', 0)
                                if isinstance(pool_stats.get('writes'), dict)
                                else pool_stats.get('writes', 0))

                worker_info = {
                    "name": worker_name,
                    "status": "online",
                    "active_tasks": worker_active_tasks,
                    "processed_tasks": processed_tasks,
                    "load_avg": load_avg[:3],
                    "pool_size": pool_size,
                    "pool_writes": pool_writes,
                    "clock": int(worker_stats.get('clock', 0))
                }
                worker_data.append(worker_info)

        # 缓存结果
        await set_cached_data(redis_client, cache_key, worker_data)

        return [CeleryWorkerInfo(**worker) for worker in worker_data]

    except Exception as e:
        logger.error(f"Failed to get workers: {e}")
        # 返回空列表而不是抛出异常
        return []


@router.get("/queues", response_model=List[QueueInfo])
async def get_queues():
    """获取队列信息"""
    try:
        redis_client = await get_redis_client()
        
        # 获取队列信息
        queues = []
        
        # 检查默认队列
        queue_length = await redis_client.llen("celery")
        
        queues.append(QueueInfo(
            name="celery",
            length=queue_length,
            consumers=1,  # 假设有一个消费者
            messages=queue_length,
            memory=queue_length * 1024  # 估算内存使用
        ))
        
        # 检查其他可能的队列
        for queue_name in ["crawl", "monitor", "high", "low"]:
            queue_length = await redis_client.llen(queue_name)
            if queue_length > 0:
                queues.append(QueueInfo(
                    name=queue_name,
                    length=queue_length,
                    consumers=1,
                    messages=queue_length,
                    memory=queue_length * 1024
                ))
        
        return queues
        
    except Exception as e:
        logger.error(f"Failed to get queues: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/revoke")
async def revoke_task(task_id: str):
    """撤销任务"""
    try:
        celery_app.control.revoke(task_id, terminate=True)
        
        # 记录撤销操作
        redis_client = await get_redis_client()
        await redis_client.hset(
            f"celery:task:{task_id}",
            mapping={
                "state": "REVOKED",
                "revoked_at": datetime.now().isoformat(),
                "revoked_by": "admin"
            }
        )
        
        return {"success": True, "message": f"Task {task_id} revoked successfully"}
        
    except Exception as e:
        logger.error(f"Failed to revoke task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/detail", response_model=CeleryTaskInfo)
async def get_task_detail(task_id: str):
    """获取任务详细信息"""
    try:
        redis_client = await get_redis_client()
        
        # 从Redis获取任务详情
        task_data = await redis_client.hgetall(f"celery:task:{task_id}")
        
        if not task_data:
            # 尝试从活跃任务中查找
            inspect = celery_app.control.inspect()
            active_tasks = inspect.active()
            
            if active_tasks:
                for worker, worker_tasks in active_tasks.items():
                    for task in worker_tasks:
                        if task['id'] == task_id:
                            return CeleryTaskInfo(
                                id=task['id'],
                                name=task['name'],
                                state='STARTED',
                                args=task.get('args', []),
                                kwargs=task.get('kwargs', {}),
                                timestamp=task.get('time_start', datetime.now().isoformat()),
                                worker=worker,
                                queue=task.get('delivery_info', {}).get('routing_key', 'default')
                            )
            
            raise HTTPException(status_code=404, detail="Task not found")
        
        return CeleryTaskInfo(
            id=task_data.get('id', task_id),
            name=task_data.get('name', ''),
            state=task_data.get('state', 'UNKNOWN'),
            args=eval(task_data.get('args', '[]')),
            kwargs=eval(task_data.get('kwargs', '{}')),
            result=task_data.get('result'),
            traceback=task_data.get('traceback'),
            timestamp=task_data.get('timestamp', ''),
            runtime=float(task_data.get('runtime', 0)) if task_data.get('runtime') else None,
            worker=task_data.get('worker'),
            queue=task_data.get('queue'),
            retries=int(task_data.get('retries', 0)) if task_data.get('retries') else None
        )
        
    except Exception as e:
        logger.error(f"Failed to get task detail {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def celery_health_check():
    """Celery健康检查"""
    try:
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if not stats:
            return {
                "status": "unhealthy",
                "message": "No workers available",
                "workers": 0,
                "timestamp": datetime.now().isoformat()
            }
        
        return {
            "status": "healthy",
            "message": "Celery is running normally",
            "workers": len(stats),
            "worker_names": list(stats.keys()),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Celery health check failed: {e}")
        return {
            "status": "unhealthy",
            "message": str(e),
            "workers": 0,
            "timestamp": datetime.now().isoformat()
        }
