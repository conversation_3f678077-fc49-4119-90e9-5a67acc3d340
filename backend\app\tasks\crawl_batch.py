"""
批量爬取任务

处理分片后的URL批次，调用外部爬虫API进行数据采集
"""

import asyncio
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from celery import Task
from celery.exceptions import Retry
import threading

from app.celery_app import celery_app
from app.core.rate_limiter import RateLimiter, RateLimitConfig
from crawler.api_client import CrawlerAPIClient, APIConfig
from app.database import get_async_session
from app.models.task import CrawlTask
from app.models.snapshot import ProductSnapshot
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

logger = logging.getLogger(__name__)


class CrawlBatchTask(Task):
    """批量爬取任务基类"""
    
    def __init__(self):
        self.rate_limiter: Optional[RateLimiter] = None
        self.api_client: Optional[CrawlerAPIClient] = None
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """重试时的回调"""
        logger.warning(f"Task {task_id} retrying due to: {exc}")
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """失败时的回调"""
        logger.error(f"Task {task_id} failed: {exc}")
    
    def on_success(self, retval, task_id, args, kwargs):
        """成功时的回调"""
        logger.info(f"Task {task_id} completed successfully")


def _run_async_in_thread(coro):
    """在新线程中运行异步协程"""
    def run_in_thread():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()

    # 在新线程中运行
    import concurrent.futures
    with concurrent.futures.ThreadPoolExecutor() as executor:
        future = executor.submit(run_in_thread)
        return future.result()


@celery_app.task(
    bind=True,
    base=CrawlBatchTask,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60},
    retry_backoff=True,
    retry_jitter=True
)
def crawl_batch_task(
    self,
    batch_id: str,
    urls: List[str],
    platform: str = "mercadolibre",
    task_id: Optional[int] = None,
    options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """批量爬取任务

    Args:
        batch_id: 批次ID
        urls: URL列表
        platform: 平台类型
        task_id: 数据库任务ID
        options: 爬取选项

    Returns:
        Dict[str, Any]: 爬取结果
    """
    return _run_async_in_thread(_crawl_batch_async(
        batch_id, urls, platform, task_id, options
    ))


async def _crawl_batch_async(
    batch_id: str,
    urls: List[str],
    platform: str,
    task_id: Optional[int],
    options: Optional[Dict[str, Any]]
) -> Dict[str, Any]:
    """异步批量爬取实现"""
    
    start_time = datetime.now()
    results = {
        "batch_id": batch_id,
        "total_urls": len(urls),
        "successful_urls": 0,
        "failed_urls": 0,
        "results": [],
        "errors": [],
        "start_time": start_time.isoformat(),
        "end_time": None,
        "duration": 0.0
    }
    
    # 初始化限流器和API客户端
    rate_limiter = RateLimiter(RateLimitConfig())

    # 检查是否有指定的爬虫后端
    crawler_backend = options.get("crawler_backend")
    if crawler_backend:
        # 使用指定的后端配置
        api_config = APIConfig(
            base_url=crawler_backend.get("base_url"),
            timeout=crawler_backend.get("timeout", 30.0),
            max_retries=crawler_backend.get("max_retries", 3),
            api_key=crawler_backend.get("api_key"),
            auth_type=crawler_backend.get("auth_type", "none")
        )
        logger.info(f"Using crawler backend: {crawler_backend.get('name')} ({crawler_backend.get('base_url')})")
    else:
        # 使用默认配置
        api_config = APIConfig.from_env()
        logger.warning("No crawler backend specified, using default configuration from environment")

    api_client = CrawlerAPIClient(api_config)
    
    try:
        await rate_limiter.start()
        await api_client.start()
        
        # 更新任务状态为运行中
        if task_id:
            await _update_task_status(task_id, "running", 0)
        
        logger.info(f"Starting batch {batch_id} with {len(urls)} URLs")
        
        # 处理URL批次
        for i, url in enumerate(urls):
            url_start_time = datetime.now()
            url_start_time_iso = url_start_time.isoformat()

            try:
                # 获取限流许可
                request_id = f"{batch_id}_{i}"
                if not await rate_limiter.acquire(request_id):
                    # 限流拒绝，等待后重试
                    await asyncio.sleep(30)
                    if not await rate_limiter.acquire(request_id):
                        raise Exception("Rate limit exceeded, unable to acquire permit")

                # 发送URL开始处理状态
                if task_id:
                    await _send_url_status_update(
                        task_id, url, "processing", f"worker_{batch_id}",
                        start_time=url_start_time_iso
                    )

                # 记录爬虫请求（用于调试）
                crawler_request_id = None
                try:
                    from ..api.crawler_debug_routes import log_crawler_api_call

                    # 构建API请求信息
                    api_request = {
                        "method": "POST",
                        "headers": {
                            "Content-Type": "application/json",
                            "User-Agent": "MonIt-Crawler/1.0"
                        },
                        "payload": {
                            "url": url,
                            "platform": platform,
                            "options": options
                        }
                    }

                    crawler_request_id = await log_crawler_api_call(
                        task_id=str(task_id),
                        url=url,
                        worker_id=f"worker_{batch_id}",
                        api_request=api_request
                    )

                except Exception as log_error:
                    logger.warning(f"Failed to log crawler request: {log_error}")

                # 调用外部API爬取
                try:
                    result = await api_client.crawl_url(url, platform, options)
                    response_time = (datetime.now() - url_start_time).total_seconds()

                    # 记录成功响应
                    if crawler_request_id:
                        try:
                            from ..api.crawler_debug_routes import log_crawler_api_response
                            await log_crawler_api_response(
                                crawler_request_id,
                                response={
                                    "status": 200,
                                    "headers": {"Content-Type": "application/json"},
                                    "data": result,
                                    "response_time": int(response_time * 1000)  # 转换为毫秒
                                }
                            )
                        except Exception as log_error:
                            logger.warning(f"Failed to log crawler response: {log_error}")

                    # 释放限流许可
                    await rate_limiter.release(request_id, True, response_time)

                    # 处理成功结果
                    results["successful_urls"] += 1
                    result_data = {
                        "url": url,
                        "success": True,
                        "data": result,
                        "response_time": response_time
                    }
                    results["results"].append(result_data)

                    # 保存到数据库
                    if task_id and result.get("data"):
                        await _save_product_snapshot(task_id, url, result["data"])

                    # 发送URL完成状态（包含结果和响应时间）
                    if task_id:
                        await _send_url_status_update(
                            task_id, url, "completed", f"worker_{batch_id}",
                            result=result_data, response_time=response_time,
                            start_time=url_start_time_iso
                        )

                    logger.debug(f"Successfully crawled {url} in {response_time:.2f}s")
                    
                except Exception as e:
                    response_time = (datetime.now() - url_start_time).total_seconds()
                    await rate_limiter.release(request_id, False, response_time)

                    # 记录失败响应
                    if crawler_request_id:
                        try:
                            from ..api.crawler_debug_routes import log_crawler_api_response
                            await log_crawler_api_response(
                                crawler_request_id,
                                error=str(e)
                            )
                        except Exception as log_error:
                            logger.warning(f"Failed to log crawler error: {log_error}")

                    # 处理失败结果
                    results["failed_urls"] += 1
                    error_info = {
                        "url": url,
                        "success": False,
                        "error": str(e),
                        "response_time": response_time
                    }
                    results["errors"].append(error_info)

                    # 发送URL失败状态（包含错误信息和响应时间）
                    if task_id:
                        await _send_url_status_update(
                            task_id, url, "failed", f"worker_{batch_id}",
                            result=error_info, response_time=response_time,
                            start_time=url_start_time_iso
                        )

                    logger.error(f"Failed to crawl {url}: {e}")
                
                # 更新进度
                if task_id:
                    progress = int((i + 1) / len(urls) * 100)
                    await _update_task_progress(task_id, progress)

                    # 发送WebSocket进度更新
                    await _send_progress_update(task_id, {
                        "total_urls": len(urls),
                        "processed_urls": i + 1,
                        "successful_urls": results["successful_urls"],
                        "failed_urls": results["failed_urls"],
                        "progress": progress,
                        "current_url": url,
                        "batch_id": batch_id
                    })
                
                # 避免过快请求
                await asyncio.sleep(1.0)
                
            except Exception as e:
                logger.error(f"Error processing URL {url}: {e}")
                results["failed_urls"] += 1
                results["errors"].append({
                    "url": url,
                    "success": False,
                    "error": str(e),
                    "response_time": 0.0
                })
        
        # 完成处理
        end_time = datetime.now()
        results["end_time"] = end_time.isoformat()
        results["duration"] = (end_time - start_time).total_seconds()
        
        # 更新任务状态
        if task_id:
            final_status = "completed" if results["failed_urls"] == 0 else "partial"
            await _update_task_status(
                task_id, 
                final_status, 
                100,
                results["successful_urls"],
                results["failed_urls"]
            )
        
        logger.info(
            f"Batch {batch_id} completed: "
            f"{results['successful_urls']}/{results['total_urls']} successful "
            f"in {results['duration']:.2f}s"
        )
        
        return results
        
    except Exception as e:
        logger.error(f"Batch {batch_id} failed: {e}")
        
        # 更新任务状态为失败
        if task_id:
            await _update_task_status(task_id, "failed", 0)
        
        results["errors"].append({
            "batch_error": str(e),
            "timestamp": datetime.now().isoformat()
        })
        
        raise
        
    finally:
        # 清理资源
        if rate_limiter:
            await rate_limiter.stop()
        if api_client:
            await api_client.close()


async def _update_task_status(
    task_id: int,
    status: str,
    progress: int,
    success_count: int = 0,
    failed_count: int = 0
) -> None:
    """更新任务状态"""
    try:
        async with get_async_session() as session:
            update_data = {
                "status": status,
                "progress": progress
            }
            
            if status == "running" and progress == 0:
                update_data["started_at"] = datetime.now()
            elif status in ["completed", "partial", "failed"]:
                update_data["completed_at"] = datetime.now()
                if success_count > 0:
                    update_data["success_count"] = success_count
                if failed_count > 0:
                    update_data["failed_count"] = failed_count
            
            await session.execute(
                update(CrawlTask)
                .where(CrawlTask.id == task_id)
                .values(**update_data)
            )
            await session.commit()
            
    except Exception as e:
        logger.error(f"Failed to update task status: {e}")


async def _update_task_progress(task_id: int, progress: int) -> None:
    """更新任务进度"""
    try:
        async with get_async_session() as session:
            await session.execute(
                update(CrawlTask)
                .where(CrawlTask.id == task_id)
                .values(progress=progress)
            )
            await session.commit()

    except Exception as e:
        logger.error(f"Failed to update task progress: {e}")


async def _send_progress_update(task_id: int, progress_data: dict) -> None:
    """发送WebSocket进度更新"""
    try:
        # 导入WebSocket管理器
        from ..main import manager

        # 构造进度消息
        message = {
            "type": "task_progress",
            "task_id": str(task_id),
            "timestamp": datetime.now().isoformat(),
            "data": progress_data
        }

        # 广播到订阅该任务的连接
        await manager.broadcast_to_task(str(task_id), json.dumps(message))

        logger.debug(f"Sent progress update for task {task_id}: {progress_data['progress']}%")

    except Exception as e:
        logger.error(f"Failed to send progress update: {e}")


async def _persist_url_execution_status(task_id: str, url: str, status: str, worker_id: str = None, result: dict = None, response_time: float = 0.0, start_time: str = None) -> None:
    """持久化URL执行状态到Redis"""
    try:
        from ..core.redis_client import get_redis_client
        redis_client = await get_redis_client()

        # URL执行状态键
        url_execution_key = f"url_execution:{task_id}:{url}"

        # 构建执行状态数据
        execution_data = {
            "task_id": task_id,
            "url": url,
            "status": status,
            "worker_id": worker_id or "unknown",
            "updated_at": datetime.now().isoformat(),
            "response_time": response_time
        }

        # 添加开始时间
        if start_time:
            execution_data["started_at"] = start_time

        # 添加完成时间
        if status in ["completed", "failed"]:
            execution_data["completed_at"] = datetime.now().isoformat()

        # 添加结果数据
        if result:
            execution_data["result"] = result
            execution_data["success"] = result.get("success", False)
            execution_data["data_extracted"] = bool(result.get("data"))

        # 存储到Redis (24小时过期)
        await redis_client.setex(
            url_execution_key,
            86400,  # 24小时
            json.dumps(execution_data, default=str, ensure_ascii=False)
        )

        # 更新任务级别的URL状态索引
        task_url_status_key = f"task_url_status:{task_id}"
        await redis_client.hset(task_url_status_key, url, status)
        await redis_client.expire(task_url_status_key, 86400)

        logger.debug(f"Persisted URL execution status: {task_id}:{url} -> {status}")

    except Exception as e:
        logger.error(f"Failed to persist URL execution status: {e}")


async def _send_url_status_update(task_id: int, url: str, status: str, worker_id: str = None, result: dict = None, response_time: float = 0.0, start_time: str = None) -> None:
    """发送URL状态更新并持久化"""
    try:
        from ..main import manager

        message = {
            "type": "url_status_change",
            "task_id": str(task_id),
            "timestamp": datetime.now().isoformat(),
            "data": {
                "url": url,
                "status": status,
                "worker_id": worker_id,
                "updated_at": datetime.now().isoformat(),
                "response_time": response_time,
                "result": result
            }
        }

        # 发送WebSocket消息
        await manager.broadcast_to_task(str(task_id), json.dumps(message))

        # 持久化状态到Redis
        await _persist_url_execution_status(
            str(task_id), url, status, worker_id, result, response_time, start_time
        )

    except Exception as e:
        logger.error(f"Failed to send URL status update: {e}")


async def _save_product_snapshot(
    task_id: int,
    url: str,
    product_data: Dict[str, Any]
) -> None:
    """保存商品快照数据"""
    try:
        async with get_async_session() as session:
            snapshot = ProductSnapshot(
                task_id=task_id,
                product_url=url,
                product_name=product_data.get("title", ""),
                current_price=product_data.get("price", 0.0),
                original_price=product_data.get("original_price", 0.0),
                discount_rate=product_data.get("discount_rate", 0.0),
                stock_quantity=product_data.get("stock", 0),
                sales_count=product_data.get("sales", 0),
                rating_score=product_data.get("rating", 0.0),
                rating_count=product_data.get("rating_count", 0),
                image_url=product_data.get("image_url", ""),
                raw_data=product_data,
                snapshot_time=datetime.now()
            )
            
            session.add(snapshot)
            await session.commit()
            
    except Exception as e:
        logger.error(f"Failed to save product snapshot: {e}")


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 30}
)
def crawl_single_url(
    self,
    url: str,
    platform: str = "mercadolibre",
    task_id: Optional[int] = None,
    options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """单URL爬取任务

    Args:
        url: 目标URL
        platform: 平台类型
        task_id: 数据库任务ID
        options: 爬取选项

    Returns:
        Dict[str, Any]: 爬取结果
    """
    return _run_async_in_thread(_crawl_single_url_async(url, platform, task_id, options))


async def _crawl_single_url_async(
    url: str,
    platform: str,
    task_id: Optional[int],
    options: Optional[Dict[str, Any]]
) -> Dict[str, Any]:
    """异步单URL爬取实现"""
    
    start_time = datetime.now()
    rate_limiter = RateLimiter(RateLimitConfig())

    # 检查是否有指定的爬虫后端
    crawler_backend = options.get("crawler_backend")
    if crawler_backend:
        # 使用指定的后端配置
        api_config = APIConfig(
            base_url=crawler_backend.get("base_url"),
            timeout=crawler_backend.get("timeout", 30.0),
            max_retries=crawler_backend.get("max_retries", 3),
            api_key=crawler_backend.get("api_key"),
            auth_type=crawler_backend.get("auth_type", "none")
        )
        logger.info(f"Using crawler backend for single URL: {crawler_backend.get('name')} ({crawler_backend.get('base_url')})")
    else:
        # 使用默认配置
        api_config = APIConfig.from_env()
        logger.warning("No crawler backend specified for single URL, using default configuration from environment")

    api_client = CrawlerAPIClient(api_config)
    
    try:
        await rate_limiter.start()
        await api_client.start()
        
        # 获取限流许可
        request_id = f"single_{int(start_time.timestamp())}"
        if not await rate_limiter.acquire(request_id):
            raise Exception("Rate limit exceeded")
        
        # 调用外部API
        try:
            result = await api_client.crawl_url(url, platform, options)
            response_time = (datetime.now() - start_time).total_seconds()
            
            await rate_limiter.release(request_id, True, response_time)
            
            # 保存结果
            if task_id and result.get("data"):
                await _save_product_snapshot(task_id, url, result["data"])
            
            return {
                "url": url,
                "success": True,
                "data": result,
                "response_time": response_time
            }
            
        except Exception as e:
            response_time = (datetime.now() - start_time).total_seconds()
            await rate_limiter.release(request_id, False, response_time)
            raise
            
    finally:
        if rate_limiter:
            await rate_limiter.stop()
        if api_client:
            await api_client.close()
