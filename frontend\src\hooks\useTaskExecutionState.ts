/**
 * 任务执行状态管理Hook
 * 提供URL级别的状态跟踪、Worker分配信息和实时更新功能
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { wsService } from '../services/websocket';

export interface UrlExecutionStatus {
  url: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  workerId?: string;
  startedAt?: string;
  completedAt?: string;
  responseTime?: number;
  result?: any;
  error?: string;
  success?: boolean;
  dataExtracted?: boolean;
}

export interface WorkerAssignment {
  workerId: string;
  urls: string[];
  status: 'idle' | 'busy' | 'offline';
  currentLoad: number;
  maxConcurrent: number;
}

export interface TaskExecutionState {
  taskId: string;
  totalUrls: number;
  urlStatuses: Record<string, UrlExecutionStatus>;
  workerAssignments: Record<string, WorkerAssignment>;
  summary: {
    total: number;
    pending: number;
    processing: number;
    completed: number;
    failed: number;
  };
  lastUpdate: string;
}

export interface UseTaskExecutionStateOptions {
  taskId?: string;
  autoConnect?: boolean;
  pollInterval?: number;
  onUrlStatusChange?: (url: string, status: UrlExecutionStatus) => void;
  onWorkerAssignmentChange?: (workerId: string, assignment: WorkerAssignment) => void;
  onTaskProgress?: (progress: { completed: number; total: number; percentage: number }) => void;
}

export function useTaskExecutionState(options: UseTaskExecutionStateOptions = {}) {
  const {
    taskId,
    autoConnect = true,
    pollInterval = 5000,
    onUrlStatusChange,
    onWorkerAssignmentChange,
    onTaskProgress
  } = options;

  const [state, setState] = useState<TaskExecutionState>({
    taskId: taskId || '',
    totalUrls: 0,
    urlStatuses: {},
    workerAssignments: {},
    summary: {
      total: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0
    },
    lastUpdate: new Date().toISOString()
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const callbacksRef = useRef({ onUrlStatusChange, onWorkerAssignmentChange, onTaskProgress });

  // 更新回调函数引用
  useEffect(() => {
    callbacksRef.current = { onUrlStatusChange, onWorkerAssignmentChange, onTaskProgress };
  });

  // 获取任务URL执行状态
  const fetchUrlExecutionStatus = useCallback(async (currentTaskId: string) => {
    if (!currentTaskId) return;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/v1/monitoring-tasks/${currentTaskId}/urls/execution-status`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch URL execution status: ${response.status}`);
      }

      const data = await response.json();
      
      // 转换数据格式
      const urlStatuses: Record<string, UrlExecutionStatus> = {};
      
      Object.entries(data.url_executions || {}).forEach(([url, execution]: [string, any]) => {
        urlStatuses[url] = {
          url,
          status: execution.status || 'pending',
          workerId: execution.worker_id,
          startedAt: execution.started_at,
          completedAt: execution.completed_at,
          responseTime: execution.response_time,
          result: execution.result,
          error: execution.result?.error,
          success: execution.result?.success,
          dataExtracted: execution.result?.data_extracted
        };
      });

      // 计算摘要信息
      const summary = data.summary || {
        total: Object.keys(urlStatuses).length,
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0
      };

      setState(prevState => ({
        ...prevState,
        taskId: currentTaskId,
        totalUrls: summary.total,
        urlStatuses,
        summary,
        lastUpdate: new Date().toISOString()
      }));

      // 触发进度回调
      if (callbacksRef.current.onTaskProgress) {
        callbacksRef.current.onTaskProgress({
          completed: summary.completed,
          total: summary.total,
          percentage: summary.total > 0 ? Math.round((summary.completed / summary.total) * 100) : 0
        });
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Failed to fetch URL execution status:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // WebSocket消息处理
  const handleWebSocketMessage = useCallback((message: any) => {
    if (message.type === 'url_status_change' && message.task_id === taskId) {
      const { url, status, worker_id, response_time, result, updated_at } = message.data;
      
      setState(prevState => {
        const newUrlStatuses = {
          ...prevState.urlStatuses,
          [url]: {
            ...prevState.urlStatuses[url],
            url,
            status,
            workerId: worker_id,
            responseTime: response_time,
            result,
            error: result?.error,
            success: result?.success,
            dataExtracted: result?.data_extracted,
            ...(status === 'completed' || status === 'failed' ? { completedAt: updated_at } : {}),
            ...(status === 'processing' && !prevState.urlStatuses[url]?.startedAt ? { startedAt: updated_at } : {})
          }
        };

        // 重新计算摘要
        const urlStatusValues = Object.values(newUrlStatuses) as UrlExecutionStatus[];
        const summary = {
          total: Object.keys(newUrlStatuses).length,
          pending: urlStatusValues.filter(s => s.status === 'pending').length,
          processing: urlStatusValues.filter(s => s.status === 'processing').length,
          completed: urlStatusValues.filter(s => s.status === 'completed').length,
          failed: urlStatusValues.filter(s => s.status === 'failed').length
        };

        // 触发URL状态变化回调
        if (callbacksRef.current.onUrlStatusChange && newUrlStatuses[url]) {
          callbacksRef.current.onUrlStatusChange(url, newUrlStatuses[url]);
        }

        // 触发进度回调
        if (callbacksRef.current.onTaskProgress) {
          callbacksRef.current.onTaskProgress({
            completed: summary.completed,
            total: summary.total,
            percentage: summary.total > 0 ? Math.round((summary.completed / summary.total) * 100) : 0
          });
        }

        return {
          ...prevState,
          urlStatuses: newUrlStatuses,
          summary,
          lastUpdate: new Date().toISOString()
        };
      });
    }
  }, [taskId]);

  // 设置WebSocket监听
  useEffect(() => {
    if (!autoConnect || !taskId) return;

    const unsubscribe = wsService.subscribe('url_status_change', handleWebSocketMessage);

    // 订阅任务状态更新
    wsService.send({
      action: 'subscribe_task',
      task_id: taskId
    });

    return () => {
      unsubscribe();
    };
  }, [taskId, autoConnect, handleWebSocketMessage]);

  // 设置轮询
  useEffect(() => {
    if (!taskId || !autoConnect) return;

    // 立即获取一次状态
    fetchUrlExecutionStatus(taskId);

    // 设置定期轮询
    if (pollInterval > 0) {
      pollIntervalRef.current = setInterval(() => {
        fetchUrlExecutionStatus(taskId);
      }, pollInterval);
    }

    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
        pollIntervalRef.current = null;
      }
    };
  }, [taskId, autoConnect, pollInterval, fetchUrlExecutionStatus]);

  // 手动刷新
  const refresh = useCallback(() => {
    if (taskId) {
      fetchUrlExecutionStatus(taskId);
    }
  }, [taskId, fetchUrlExecutionStatus]);

  // 获取特定URL的状态
  const getUrlStatus = useCallback((url: string): UrlExecutionStatus | undefined => {
    return state.urlStatuses[url];
  }, [state.urlStatuses]);

  // 获取Worker分配信息
  const getWorkerAssignments = useCallback((): WorkerAssignment[] => {
    return Object.values(state.workerAssignments);
  }, [state.workerAssignments]);

  // 获取按状态分组的URL
  const getUrlsByStatus = useCallback((status: UrlExecutionStatus['status']): UrlExecutionStatus[] => {
    return (Object.values(state.urlStatuses) as UrlExecutionStatus[]).filter(urlStatus => urlStatus.status === status);
  }, [state.urlStatuses]);

  return {
    state,
    loading,
    error,
    refresh,
    getUrlStatus,
    getWorkerAssignments,
    getUrlsByStatus,
    // 便捷的状态访问
    urlStatuses: state.urlStatuses,
    summary: state.summary,
    totalUrls: state.totalUrls,
    lastUpdate: state.lastUpdate
  };
}
