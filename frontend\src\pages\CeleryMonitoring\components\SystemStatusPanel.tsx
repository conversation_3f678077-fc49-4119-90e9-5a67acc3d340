import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Tag,
  Space,
  Button,
  Alert,
  Descriptions,
  Typography,
  Tooltip,
  Spin
} from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  DatabaseOutlined,
  CloudServerOutlined,
  TeamOutlined,
  BarsOutlined
} from '@ant-design/icons';

const { Text, Title } = Typography;

interface SystemOverview {
  containers: {
    worker: { status: string; uptime: string };
    beat: { status: string; uptime: string };
  };
  redis: { connected: boolean; queues: number };
  tasks: { active: number; failed: number; pending: number };
  workers: { online: number; total: number };
}

interface DockerContainer {
  name: string;
  status: string;
  state: string;
  uptime: string;
  restart_count: number;
}

interface SystemStatusPanelProps {
  systemOverview: SystemOverview | null;
}

const SystemStatusPanel: React.FC<SystemStatusPanelProps> = ({ systemOverview }) => {
  const [containers, setContainers] = useState<DockerContainer[]>([]);
  const [redisStats, setRedisStats] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // 获取Docker容器状态
  const fetchContainers = async () => {
    try {
      const response = await fetch('/api/v1/celery/docker/containers');
      if (response.ok) {
        const data = await response.json();
        setContainers(data);
      }
    } catch (error) {
      console.error('Failed to fetch containers:', error);
    }
  };

  // 获取Redis统计信息
  const fetchRedisStats = async () => {
    try {
      const response = await fetch('/api/v1/celery/redis/info');
      if (response.ok) {
        const data = await response.json();
        setRedisStats(data);
      }
    } catch (error) {
      console.error('Failed to fetch Redis stats:', error);
    }
  };

  // 刷新所有数据
  const handleRefresh = async () => {
    setLoading(true);
    try {
      await Promise.all([fetchContainers(), fetchRedisStats()]);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Failed to refresh system status:', error);
    } finally {
      setLoading(false);
    }
  };

  // 监听刷新事件
  useEffect(() => {
    const handleGlobalRefresh = () => {
      handleRefresh();
    };

    window.addEventListener('celery-refresh', handleGlobalRefresh);
    return () => window.removeEventListener('celery-refresh', handleGlobalRefresh);
  }, []);

  // 初始化数据
  useEffect(() => {
    handleRefresh();
  }, []);

  // 获取容器状态图标和颜色
  const getContainerStatusIcon = (state: string) => {
    switch (state.toLowerCase()) {
      case 'running':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'exited':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
    }
  };

  const getContainerStatusColor = (state: string) => {
    switch (state.toLowerCase()) {
      case 'running':
        return 'success';
      case 'exited':
        return 'error';
      default:
        return 'warning';
    }
  };

  // 计算系统健康度
  const calculateSystemHealth = () => {
    if (!systemOverview || containers.length === 0) return 0;

    let healthScore = 0;
    const maxScore = 100;

    // Docker容器状态 (40分)
    const runningContainers = containers.filter(c => c.state === 'running').length;
    const totalContainers = containers.length;
    healthScore += (runningContainers / totalContainers) * 40;

    // Redis连接状态 (20分)
    if (systemOverview.redis.connected) {
      healthScore += 20;
    }

    // Worker在线状态 (20分)
    if (systemOverview.workers.online > 0) {
      healthScore += 20;
    }

    // 任务失败率 (20分)
    const totalTasks = systemOverview.tasks.active + systemOverview.tasks.failed + systemOverview.tasks.pending;
    if (totalTasks > 0) {
      const failureRate = systemOverview.tasks.failed / totalTasks;
      healthScore += (1 - failureRate) * 20;
    } else {
      healthScore += 20; // 没有任务时给满分
    }

    return Math.round(healthScore);
  };

  const systemHealth = calculateSystemHealth();

  return (
    <div className="system-status-panel">
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={4} style={{ margin: 0 }}>系统状态监控</Title>
          <Text type="secondary">
            最后更新: {lastUpdate.toLocaleTimeString()}
          </Text>
        </Col>
        <Col>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
            size="small"
          >
            刷新状态
          </Button>
        </Col>
      </Row>

      {/* 系统健康度 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card size="small">
            <Row align="middle">
              <Col span={18}>
                <div>
                  <Text strong>系统健康度</Text>
                  <div style={{ marginTop: 8 }}>
                    <Progress
                      percent={systemHealth}
                      status={systemHealth >= 80 ? 'success' : systemHealth >= 60 ? 'normal' : 'exception'}
                      strokeColor={
                        systemHealth >= 80 ? '#52c41a' : 
                        systemHealth >= 60 ? '#1890ff' : '#ff4d4f'
                      }
                    />
                  </div>
                </div>
              </Col>
              <Col span={6} style={{ textAlign: 'right' }}>
                <Statistic
                  value={systemHealth}
                  suffix="%"
                  valueStyle={{
                    color: systemHealth >= 80 ? '#52c41a' : 
                           systemHealth >= 60 ? '#1890ff' : '#ff4d4f'
                  }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* Docker容器状态 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Card 
            title={
              <Space>
                <CloudServerOutlined />
                Docker容器状态
              </Space>
            }
            size="small"
          >
            {containers.length === 0 ? (
              <Spin />
            ) : (
              <Row gutter={16}>
                {containers.map((container) => (
                  <Col span={12} key={container.name}>
                    <Card size="small" style={{ marginBottom: 8 }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                          <Space>
                            {getContainerStatusIcon(container.state)}
                            <Text strong>{container.name}</Text>
                          </Space>
                          <div style={{ marginTop: 4 }}>
                            <Tag color={getContainerStatusColor(container.state)}>
                              {container.state}
                            </Tag>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              运行时间: {container.uptime}
                            </Text>
                          </div>
                        </div>
                        <div style={{ textAlign: 'right' }}>
                          <Tooltip title="重启次数">
                            <Text type="secondary">
                              重启: {container.restart_count}
                            </Text>
                          </Tooltip>
                        </div>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            )}
          </Card>
        </Col>
      </Row>

      {/* 系统统计信息 */}
      <Row gutter={16}>
        <Col span={12}>
          <Card
            title={
              <Space>
                <DatabaseOutlined />
                Redis状态
              </Space>
            }
            size="small"
          >
            <Descriptions column={1} size="small">
              <Descriptions.Item label="连接状态">
                {systemOverview?.redis.connected ? (
                  <Tag color="success" icon={<CheckCircleOutlined />}>已连接</Tag>
                ) : (
                  <Tag color="error" icon={<CloseCircleOutlined />}>断开连接</Tag>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="队列数量">
                {systemOverview?.redis.queues || 0}
              </Descriptions.Item>
              {redisStats && (
                <>
                  <Descriptions.Item label="内存使用">
                    {redisStats.memory_usage || 'N/A'}
                  </Descriptions.Item>
                  <Descriptions.Item label="连接数">
                    {redisStats.connected_clients || 'N/A'}
                  </Descriptions.Item>
                </>
              )}
            </Descriptions>
          </Card>
        </Col>
        <Col span={12}>
          <Card
            title={
              <Space>
                <BarsOutlined />
                任务统计
              </Space>
            }
            size="small"
          >
            <Row gutter={8}>
              <Col span={8}>
                <Statistic
                  title="活跃"
                  value={systemOverview?.tasks.active || 0}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="等待"
                  value={systemOverview?.tasks.pending || 0}
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="失败"
                  value={systemOverview?.tasks.failed || 0}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 系统警告 */}
      {systemHealth < 80 && (
        <Alert
          message="系统状态警告"
          description={
            <div>
              {systemHealth < 60 && "系统存在严重问题，请立即检查！"}
              {systemHealth >= 60 && systemHealth < 80 && "系统状态不佳，建议检查相关服务。"}
              <ul style={{ marginTop: 8, marginBottom: 0 }}>
                {containers.some(c => c.state !== 'running') && (
                  <li>部分Docker容器未正常运行</li>
                )}
                {!systemOverview?.redis.connected && (
                  <li>Redis连接异常</li>
                )}
                {systemOverview?.workers.online === 0 && (
                  <li>没有在线的Worker</li>
                )}
                {systemOverview && systemOverview.tasks.failed > 0 && (
                  <li>存在失败的任务</li>
                )}
              </ul>
            </div>
          }
          type={systemHealth < 60 ? 'error' : 'warning'}
          showIcon
          style={{ marginTop: 16 }}
        />
      )}
    </div>
  );
};

export default SystemStatusPanel;
