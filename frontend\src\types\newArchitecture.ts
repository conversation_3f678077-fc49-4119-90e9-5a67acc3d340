/**
 * 新架构类型定义
 * 包含爬取配置、后端配置、Worker管理等相关类型
 */

// ==================== 基础类型 ====================

export interface BaseEntity {
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  updated_by?: string;
}

// ==================== 爬取配置相关 ====================

export interface BrowserConfig {
  headless: boolean;
  verbose: boolean;
  viewport_width: number;
  viewport_height: number;
  wait_for: number;
  timeout: number;
  ignore_https_errors: boolean;
  extra_args: string[];
}

export interface CrawlerConfig {
  method: string;
  verbose: boolean;
  check_robots_txt: boolean;
  fetch_ssl_certificate: boolean;
  simulate_user: boolean;
  magic: boolean;
  override_navigator: boolean;
  remove_overlay_elements: boolean;
  ignore_body_visibility: boolean;
  adjust_viewport_to_content: boolean;
  wait_until: string;
  wait_for_images: boolean;
  page_timeout: number;
  delay_before_return_html: number;
  js_only: boolean;
  scan_full_page: boolean;
  process_iframes: boolean;
  scroll_delay: number;
  cache_mode: string;
  screenshot: boolean;
  pdf: boolean;
  capture_mhtml: boolean;
  exclude_external_images: boolean;
  exclude_all_images: boolean;
  image_score_threshold: number;
  image_description_min_word_threshold: number;
  table_score_threshold: number;
  capture_network_requests: boolean;
  capture_console_messages: boolean;
  log_console: boolean;
  extraction_strategy: string;
  chunking_strategy: string;
  markdown_generator: string;
  bypass_cache: boolean;
}

export interface LLMConfig {
  query: string;
  provider: string;
  model: string;
  api_key: string;
  base_url: string;
  temperature: number;
  max_tokens: number;
  top_p: number;
}

export interface SchemaExtractionConfig {
  enabled: boolean;
  schema_type: string;
  validate_schema: boolean;
  return_raw: boolean;
  extraction_schema?: any;
  instructions?: string;
}

export interface ContentProcessingConfig {
  word_count_threshold: number;
  css_selector: string;
  target_elements: string[];
  excluded_tags: string[];
  excluded_selector: string;
  remove_forms: boolean;
  only_text: boolean;
  prettify: boolean;
  parser_type: string;
  keep_data_attributes: boolean;
  keep_attrs: string[];
}

export interface LinkFilteringConfig {
  exclude_external_links: boolean;
  exclude_internal_links: boolean;
  exclude_social_media_links: boolean;
  exclude_domains: string[];
  social_media_domains: string[];
  exclude_external_images: boolean;
  exclude_all_images: boolean;
  image_score_threshold: number;
  image_description_min_word_threshold: number;
  table_score_threshold: number;
}

export interface MonitorConfig {
  display_mode: string;
  show_progress: boolean;
  log_errors: boolean;
}

export type ConfigStatus = 'draft' | 'active' | 'inactive' | 'deprecated';

export interface CrawlerConfigData extends BaseEntity {
  config_id: string;
  config_name: string;
  description?: string;
  status: ConfigStatus;
  browser: BrowserConfig;
  crawler: CrawlerConfig;
  llm: LLMConfig;
  schema_extraction: SchemaExtractionConfig;
  content_processing: ContentProcessingConfig;
  link_filtering: LinkFilteringConfig;
  monitor: MonitorConfig;
  version: string;
  tags: string[];
  usage_count: number;
  last_used?: string;
}

export interface CrawlerConfigCreate {
  config_name: string;
  description?: string;
  browser: BrowserConfig;
  crawler: CrawlerConfig;
  llm: LLMConfig;
  schema_extraction: SchemaExtractionConfig;
  content_processing: ContentProcessingConfig;
  link_filtering: LinkFilteringConfig;
  monitor: MonitorConfig;
  version?: string;
  tags?: string[];
  created_by?: string;
}

export interface CrawlerConfigUpdate {
  config_name?: string;
  description?: string;
  status?: ConfigStatus;
  browser?: Partial<BrowserConfig>;
  crawler?: Partial<CrawlerConfig>;
  llm?: Partial<LLMConfig>;
  schema_extraction?: Partial<SchemaExtractionConfig>;
  content_processing?: Partial<ContentProcessingConfig>;
  link_filtering?: Partial<LinkFilteringConfig>;
  monitor?: Partial<MonitorConfig>;
  tags?: string[];
}

export interface CrawlerConfigSummary {
  config_id: string;
  config_name: string;
  description?: string;
  status: ConfigStatus;
  version: string;
  tags: string[];
  usage_count: number;
  last_used?: string;
  created_at?: string;
  updated_at?: string;
}

export interface CrawlerConfigValidationResult {
  is_valid: boolean;
  errors: string[];
  warnings: string[];
  config_id: string;
  validation_time: string;
}

// ==================== 后端配置相关 ====================

export interface AuthConfig {
  auth_type: 'none' | 'api_key' | 'basic' | 'bearer';
  api_key?: string;
  username?: string;
  password?: string;
  token?: string;
}

export type BackendStatus = 'active' | 'inactive' | 'error' | 'maintenance';
export type PerformanceLevel = 'low' | 'medium' | 'high' | 'ultra';

export interface BackendConfigData extends BaseEntity {
  backend_id: string;
  backend_name: string;
  description?: string;
  api_endpoint: string;
  timeout: number;
  max_retries: number;
  auth_config: AuthConfig;
  max_concurrent: number;
  mean_delay: number;
  max_range: number;
  pool_size: number;
  memory_threshold: number;
  weight: number;
  priority: number;
  performance_level: PerformanceLevel;
  health_check_interval: number;
  health_check_timeout: number;
  failure_threshold: number;
  recovery_threshold: number;
  status: BackendStatus;
  health_score: number;
  last_health_check?: string;
  total_requests: number;
  success_requests: number;
  failed_requests: number;
  avg_response_time: number;
  current_load: number;
}

export interface BackendConfigCreate {
  backend_name: string;
  description?: string;
  api_endpoint: string;
  timeout?: number;
  max_retries?: number;
  auth_config?: AuthConfig;
  max_concurrent?: number;
  mean_delay?: number;
  max_range?: number;
  pool_size?: number;
  memory_threshold?: number;
  weight?: number;
  priority?: number;
  performance_level?: PerformanceLevel;
  health_check_interval?: number;
  health_check_timeout?: number;
  failure_threshold?: number;
  recovery_threshold?: number;
}

export interface BackendConfigUpdate {
  backend_name?: string;
  description?: string;
  api_endpoint?: string;
  timeout?: number;
  max_retries?: number;
  auth_config?: AuthConfig;
  max_concurrent?: number;
  mean_delay?: number;
  max_range?: number;
  pool_size?: number;
  memory_threshold?: number;
  weight?: number;
  priority?: number;
  performance_level?: PerformanceLevel;
  health_check_interval?: number;
  health_check_timeout?: number;
  failure_threshold?: number;
  recovery_threshold?: number;
  status?: BackendStatus;
}

export interface BackendConfigSummary {
  backend_id: string;
  backend_name: string;
  description?: string;
  api_endpoint: string;
  status: BackendStatus;
  performance_level: PerformanceLevel;
  health_score: number;
  max_concurrent: number;
  current_load: number;
  total_requests: number;
  success_rate: number;
  avg_response_time: number;
  created_at?: string;
  updated_at?: string;
}

export interface BackendHealthCheck {
  backend_id: string;
  is_healthy: boolean;
  response_time: number;
  check_time: string;
  error_message?: string;
  api_version?: string;
  server_info?: any;
  load_info?: any;
  health_score: number;
}

export interface BackendConfigStats {
  total_backends: number;
  active_backends: number;
  healthy_backends: number;
  available_backends: number;
  total_capacity: number;
  used_capacity: number;
  available_capacity: number;
  avg_health_score: number;
  avg_response_time: number;
  total_success_rate: number;
  performance_distribution: Record<string, number>;
  recently_created: BackendConfigSummary[];
  recently_failed: BackendConfigSummary[];
}

// ==================== Worker相关 ====================

export type WorkerStatus = 'active' | 'idle' | 'running' | 'busy' | 'error' | 'maintenance';
export type WorkerPriority = 'low' | 'normal' | 'high' | 'urgent';

export interface CrawlerWorker extends BaseEntity {
  worker_id: string;
  worker_name: string;
  description?: string;
  crawler_config_id: string;
  backend_config_id: string;
  status: WorkerStatus;
  max_concurrent_tasks: number;
  current_tasks: number;
  allocated_concurrent: number;
  priority: WorkerPriority;
  tags: string[];
  last_heartbeat?: string;
  total_tasks_completed: number;
  total_tasks_failed: number;
  avg_task_duration: number;
  health_score: number;

  // 配置名称（从后端获取）
  crawler_config_name?: string;
  backend_config_name?: string;
}

export interface CrawlerWorkerCreate {
  worker_name: string;
  description?: string;
  crawler_config_id: string;
  backend_config_id: string;
  max_concurrent_tasks?: number;
  allocated_concurrent?: number;
  priority?: WorkerPriority;
  tags?: string[];
}

export interface CrawlerWorkerUpdate {
  worker_name?: string;
  description?: string;
  crawler_config_id?: string;
  backend_config_id?: string;
  max_concurrent_tasks?: number;
  allocated_concurrent?: number;
  priority?: WorkerPriority;
  status?: WorkerStatus;
  tags?: string[];
}

export interface CrawlerWorkerSummary {
  worker_id: string;
  worker_name: string;
  description?: string;
  status: WorkerStatus;
  max_concurrent_tasks: number;
  current_tasks: number;
  allocated_concurrent: number;
  priority: WorkerPriority;
  tags: string[];
  health_score: number;
  total_tasks_completed: number;
  success_rate: number;
  created_at?: string;
  updated_at?: string;

  // 配置信息
  crawler_config_name?: string;
  backend_config_name?: string;
  crawler_config_id?: string;
  backend_config_id?: string;
}

export interface WorkerCompatibilityCheck {
  worker_id: string;
  is_compatible: boolean;
  crawler_compatible: boolean;
  backend_available: boolean;
  resource_conflict: boolean;
  errors: string[];
  warnings: string[];
  conflicted_workers: string[];
}

export interface WorkerGroupCompatibilityCheck {
  worker_ids: string[];
  is_compatible: boolean;

  // 检查结果
  individual_checks: WorkerCompatibilityCheck[];
  resource_conflicts: Array<{
    type: string;
    backend_id?: string;
    workers?: string[];
    required_concurrent?: number;
    max_concurrent?: number;
  }>;

  // 统计信息
  total_workers: number;
  valid_workers: number;
  conflicted_workers: number;

  // 建议
  recommended_workers: string[];
  alternative_workers: string[];
}

// ==================== 任务分配相关 ====================

export type AssignmentStatus = 'pending' | 'assigned' | 'running' | 'completed' | 'failed' | 'cancelled';
export type AssignmentStrategy = 'round_robin' | 'least_loaded' | 'priority_based' | 'manual';

export interface TaskAssignment extends BaseEntity {
  assignment_id: string;
  task_id: string;
  worker_id: string;
  status: AssignmentStatus;
  strategy: AssignmentStrategy;
  priority: number;
  estimated_duration?: number;
  actual_duration?: number;
  assigned_at?: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  retry_count: number;
  max_retries: number;
}

export interface TaskAssignmentCreate {
  task_id: string;
  worker_id?: string;
  strategy?: AssignmentStrategy;
  priority?: number;
  estimated_duration?: number;
  max_retries?: number;
}

export interface AssignmentRecommendation {
  worker_id: string;
  worker_name: string;
  compatibility_score: number;
  estimated_completion_time: string;
  current_load: number;
  reasons: string[];
}

// ==================== API响应类型 ====================

export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface ApiError {
  detail: string;
  errors?: string[];
  error_type?: string;
}
