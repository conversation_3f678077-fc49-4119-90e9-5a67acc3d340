import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { store } from './store';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import MonitoringTasks from './pages/MonitoringTasks';
import TaskDetail from './pages/MonitoringTasks/TaskDetail';
import UrlPool from './pages/UrlPool';
import TaskEdit from './pages/TaskEdit';
import DataAnalysis from './pages/DataAnalysis';
import Configuration from './pages/Configuration';
import CrawlerConfiguration from './pages/Configuration/CrawlerConfiguration';
import BackendConfiguration from './pages/Configuration/BackendConfiguration';
import Logs from './pages/Logs';
import CrawlerSettings from './pages/CrawlerSettings';
import CeleryMonitoring from './pages/CeleryMonitoring';
import CeleryMonitoringV2 from './pages/CeleryMonitoring/CeleryMonitoringV2';
import CeleryMonitoringV3 from './pages/CeleryMonitoring/CeleryMonitoringV3';
import TaskManagerMigration from './pages/TaskManagerMigration';
import AlertManagement from './pages/AlertManagement';
import WorkerManagement from './pages/WorkerManagement';
import { useTaskUpdates, useSystemMonitoring } from './hooks';
import './App.css';

// 主应用组件
const AppContent: React.FC = () => {
  // 启用实时更新
  useTaskUpdates();
  useSystemMonitoring();

  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/monitoring" element={<MonitoringTasks />} />
          <Route path="/monitoring/tasks/:taskId" element={<TaskDetail />} />
          <Route path="/monitoring/tasks/:taskId/edit" element={<TaskEdit />} />
          <Route path="/url-pool" element={<UrlPool />} />
          <Route path="/data-analysis" element={<DataAnalysis />} />
          <Route path="/config" element={<Configuration />} />
          <Route path="/config/crawler" element={<CrawlerConfiguration />} />
          <Route path="/config/backend" element={<BackendConfiguration />} />
          <Route path="/worker-management" element={<WorkerManagement />} />
          <Route path="/crawler-settings" element={<CrawlerSettings />} />
          <Route path="/celery-monitoring" element={<CeleryMonitoringV3 />} />
          <Route path="/celery-monitoring/v2" element={<CeleryMonitoringV2 />} />
          <Route path="/celery-monitoring/legacy" element={<CeleryMonitoring />} />
          <Route path="/alerts" element={<AlertManagement />} />
          <Route path="/logs" element={<Logs />} />
          {/* 旧任务管理系统迁移页面 */}
          <Route path="/tasks" element={<TaskManagerMigration />} />
          <Route path="/tasks/create" element={<Navigate to="/url-pool" replace />} />
          {/* 其他兼容旧路径 */}
          <Route path="/data" element={<Navigate to="/data-analysis" replace />} />
        </Routes>
      </Layout>
    </Router>
  );
};

function App() {
  return (
    <Provider store={store}>
      <ConfigProvider locale={zhCN}>
        <AntdApp>
          <AppContent />
        </AntdApp>
      </ConfigProvider>
    </Provider>
  );
}

export default App;
