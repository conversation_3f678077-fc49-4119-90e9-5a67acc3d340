import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Table,
  Button,
  Space,
  Typography,
  Alert,
  Badge,
  Tag,
  Tooltip,
  Descriptions,
  Modal,
  message,
  Select,
  DatePicker,
  Row,
  Col,
  Collapse
} from 'antd';
import {
  BugOutlined,
  SendOutlined,
  EyeOutlined,
  CopyOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  StopOutlined,
  FilterOutlined
} from '@ant-design/icons';

const { Text } = Typography;
const { Panel } = Collapse;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface CrawlerRequest {
  id: string;
  taskId: string;
  url: string;
  workerId: string;
  timestamp: string;
  method: string;
  headers: Record<string, string>;
  payload: any;
  response?: {
    status: number;
    headers: Record<string, string>;
    data: any;
    responseTime: number;
  };
  error?: string;
  status: 'pending' | 'sent' | 'success' | 'failed';
}

interface CrawlerWorkerInfo {
  workerId: string;
  version: string;
  endpoint: string;
  status: 'online' | 'offline' | 'busy';
  capabilities: string[];
  lastSeen: string;
}

const CrawlerRequestPanel: React.FC = () => {
  const [requests, setRequests] = useState<CrawlerRequest[]>([]);
  const [workers, setWorkers] = useState<CrawlerWorkerInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<CrawlerRequest | null>(null);
  const [isLiveMode, setIsLiveMode] = useState(false);
  const [filters, setFilters] = useState({
    workerId: '',
    status: '',
    timeRange: null as any
  });

  // 🔧 MOCK DATA - 模拟数据，用于演示功能
  const [useMockData] = useState(true);

  // 🔧 MOCK DATA - 模拟Worker数据
  const getMockWorkers = (): CrawlerWorkerInfo[] => [
    {
      workerId: 'crawler-worker-v1.0',
      version: '1.0.0',
      endpoint: 'http://crawler-api-v1:8080',
      status: 'online',
      capabilities: ['taobao', 'tmall', 'jd'],
      lastSeen: new Date(Date.now() - 30000).toISOString()
    },
    {
      workerId: 'crawler-worker-v2.0',
      version: '2.0.0',
      endpoint: 'http://crawler-api-v2:8080',
      status: 'online',
      capabilities: ['taobao', 'tmall', 'jd', 'pdd'],
      lastSeen: new Date(Date.now() - 60000).toISOString()
    }
  ];

  // 🔧 MOCK DATA - 模拟请求数据
  const getMockRequests = (): CrawlerRequest[] => [
    {
      id: 'req_001',
      taskId: '123',
      url: 'https://detail.tmall.com/item.htm?id=123456789',
      workerId: 'crawler-worker-v1.0',
      timestamp: new Date(Date.now() - 120000).toISOString(),
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'MonIt-Crawler/1.0'
      },
      payload: {
        url: 'https://detail.tmall.com/item.htm?id=123456789',
        platform: 'tmall',
        options: { extract_images: true, extract_reviews: false }
      },
      response: {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        data: {
          title: '苹果iPhone 15 Pro Max',
          price: 9999.00,
          stock: 100,
          rating: 4.8
        },
        responseTime: 2500
      },
      status: 'success'
    },
    {
      id: 'req_002',
      taskId: '123',
      url: 'https://item.jd.com/100012345678.html',
      workerId: 'crawler-worker-v2.0',
      timestamp: new Date(Date.now() - 60000).toISOString(),
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'MonIt-Crawler/2.0'
      },
      payload: {
        url: 'https://item.jd.com/100012345678.html',
        platform: 'jd',
        options: { extract_images: true, extract_reviews: true }
      },
      status: 'sent'
    }
  ];

  // 获取请求历史
  const fetchRequestHistory = async () => {
    try {
      setLoading(true);
      
      if (useMockData) {
        await new Promise(resolve => setTimeout(resolve, 500));
        let mockRequests = getMockRequests();
        
        if (filters.workerId) {
          mockRequests = mockRequests.filter(req => req.workerId === filters.workerId);
        }
        if (filters.status) {
          mockRequests = mockRequests.filter(req => req.status === filters.status);
        }
        
        setRequests(mockRequests);
        return;
      }

      // TODO: 真实API调用
      const response = await fetch('/api/v1/crawler-debug/requests');
      if (response.ok) {
        const data = await response.json();
        setRequests(data);
      }
    } catch (error) {
      console.error('Failed to fetch request history:', error);
      setRequests(getMockRequests());
    } finally {
      setLoading(false);
    }
  };

  // 获取Worker信息
  const fetchWorkers = async () => {
    try {
      if (useMockData) {
        setWorkers(getMockWorkers());
        return;
      }

      const response = await fetch('/api/v1/crawler-workers');
      if (response.ok) {
        const data = await response.json();
        setWorkers(data);
      }
    } catch (error) {
      console.error('Failed to fetch workers:', error);
      setWorkers(getMockWorkers());
    }
  };

  // 🔧 MOCK IMPLEMENTATION - 模拟实时监听
  const startLiveMode = () => {
    setIsLiveMode(true);
    if (useMockData) {
      message.success('🔧 实时监听已开启 (演示模式)');
    } else {
      message.success('实时监听已开启');
    }
  };

  const stopLiveMode = () => {
    setIsLiveMode(false);
    message.info('实时监听已停止');
  };

  // 复制请求信息
  const copyRequest = (request: CrawlerRequest) => {
    const requestInfo = {
      url: request.url,
      method: request.method,
      headers: request.headers,
      payload: request.payload
    };
    
    navigator.clipboard.writeText(JSON.stringify(requestInfo, null, 2));
    message.success('请求信息已复制到剪贴板');
  };

  useEffect(() => {
    fetchWorkers();
    fetchRequestHistory();
  }, []);

  useEffect(() => {
    fetchRequestHistory();
  }, [filters]);

  // 请求列表表格列定义
  const requestColumns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 140,
      render: (timestamp: string) => new Date(timestamp).toLocaleString()
    },
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      ellipsis: true,
      render: (url: string) => (
        <Tooltip title={url}>
          <Text code style={{ fontSize: '12px' }}>
            {url.length > 40 ? `${url.substring(0, 40)}...` : url}
          </Text>
        </Tooltip>
      )
    },
    {
      title: 'Worker',
      dataIndex: 'workerId',
      key: 'workerId',
      width: 120,
      render: (workerId: string) => <Tag color="blue">{workerId}</Tag>
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => {
        const colors = {
          pending: 'orange',
          sent: 'blue',
          success: 'green',
          failed: 'red'
        };
        return <Badge color={colors[status as keyof typeof colors]} text={status} />;
      }
    },
    {
      title: '响应时间',
      dataIndex: ['response', 'responseTime'],
      key: 'responseTime',
      width: 80,
      render: (time: number) => time ? `${time}ms` : '-'
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_: any, record: CrawlerRequest) => (
        <Space>
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => setSelectedRequest(record)}
          />
          <Button
            size="small"
            icon={<CopyOutlined />}
            onClick={() => copyRequest(record)}
          />
        </Space>
      )
    }
  ];

  return (
    <div>
      {/* 功能说明 */}
      <Alert
        message="爬虫请求调试"
        description={
          <div>
            监控Celery Worker发送给爬虫后端Worker的请求详情，便于调试不同版本爬虫Worker的兼容性问题。
            {useMockData && (
              <Text type="warning" style={{ marginLeft: 8 }}>
                🔧 当前显示模拟数据，用于功能演示
              </Text>
            )}
          </div>
        }
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* 控制面板 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={16}>
          <Card size="small" title="过滤条件">
            <Row gutter={8} align="middle">
              <Col span={6}>
                <Select
                  placeholder="选择Worker"
                  value={filters.workerId}
                  onChange={(value) => setFilters(prev => ({ ...prev, workerId: value }))}
                  style={{ width: '100%' }}
                  allowClear
                  size="small"
                >
                  {workers.map(worker => (
                    <Option key={worker.workerId} value={worker.workerId}>
                      {worker.workerId}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={6}>
                <Select
                  placeholder="请求状态"
                  value={filters.status}
                  onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
                  style={{ width: '100%' }}
                  allowClear
                  size="small"
                >
                  <Option value="pending">等待中</Option>
                  <Option value="sent">已发送</Option>
                  <Option value="success">成功</Option>
                  <Option value="failed">失败</Option>
                </Select>
              </Col>
              <Col span={8}>
                <RangePicker
                  showTime
                  value={filters.timeRange}
                  onChange={(dates) => setFilters(prev => ({ ...prev, timeRange: dates }))}
                  style={{ width: '100%' }}
                  size="small"
                />
              </Col>
              <Col span={4}>
                <Button
                  icon={<FilterOutlined />}
                  onClick={fetchRequestHistory}
                  loading={loading}
                  size="small"
                  block
                >
                  应用
                </Button>
              </Col>
            </Row>
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small" title="实时监听">
            <Space>
              {!isLiveMode ? (
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={startLiveMode}
                  size="small"
                >
                  开始监听
                </Button>
              ) : (
                <Button
                  icon={<StopOutlined />}
                  onClick={stopLiveMode}
                  size="small"
                >
                  停止监听
                </Button>
              )}
              <Button 
                icon={<ReloadOutlined />} 
                onClick={fetchRequestHistory}
                loading={loading}
                size="small"
              >
                刷新
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 请求列表 */}
      <Card title="爬虫请求记录" size="small">
        <Table
          columns={requestColumns}
          dataSource={requests}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 8, showSizeChanger: false }}
          scroll={{ x: 800 }}
          size="small"
        />
      </Card>

      {/* 请求详情模态框 */}
      <Modal
        title="请求详情"
        open={!!selectedRequest}
        onCancel={() => setSelectedRequest(null)}
        footer={[
          <Button key="copy" icon={<CopyOutlined />} onClick={() => selectedRequest && copyRequest(selectedRequest)}>
            复制
          </Button>,
          <Button key="close" onClick={() => setSelectedRequest(null)}>
            关闭
          </Button>
        ]}
        width={700}
      >
        {selectedRequest && (
          <Collapse defaultActiveKey={['request']}>
            <Panel header="请求信息" key="request">
              <Descriptions column={2} size="small">
                <Descriptions.Item label="URL">{selectedRequest.url}</Descriptions.Item>
                <Descriptions.Item label="方法">{selectedRequest.method}</Descriptions.Item>
                <Descriptions.Item label="Worker">{selectedRequest.workerId}</Descriptions.Item>
                <Descriptions.Item label="时间">{new Date(selectedRequest.timestamp).toLocaleString()}</Descriptions.Item>
              </Descriptions>
              
              <div style={{ marginTop: 16 }}>
                <Text strong>请求头:</Text>
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: 8, 
                  borderRadius: 4, 
                  marginTop: 8,
                  fontSize: '12px'
                }}>
                  {JSON.stringify(selectedRequest.headers, null, 2)}
                </pre>
              </div>
              
              <div style={{ marginTop: 16 }}>
                <Text strong>请求体:</Text>
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: 8, 
                  borderRadius: 4, 
                  marginTop: 8,
                  fontSize: '12px'
                }}>
                  {JSON.stringify(selectedRequest.payload, null, 2)}
                </pre>
              </div>
            </Panel>
            
            {selectedRequest.response && (
              <Panel header="响应信息" key="response">
                <Descriptions column={2} size="small">
                  <Descriptions.Item label="状态码">{selectedRequest.response.status}</Descriptions.Item>
                  <Descriptions.Item label="响应时间">{selectedRequest.response.responseTime}ms</Descriptions.Item>
                </Descriptions>
                
                <div style={{ marginTop: 16 }}>
                  <Text strong>响应数据:</Text>
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: 8, 
                    borderRadius: 4, 
                    marginTop: 8,
                    fontSize: '12px',
                    maxHeight: 200,
                    overflow: 'auto'
                  }}>
                    {JSON.stringify(selectedRequest.response.data, null, 2)}
                  </pre>
                </div>
              </Panel>
            )}
            
            {selectedRequest.error && (
              <Panel header="错误信息" key="error">
                <Alert
                  message="请求失败"
                  description={selectedRequest.error}
                  type="error"
                  showIcon
                />
              </Panel>
            )}
          </Collapse>
        )}
      </Modal>
    </div>
  );
};

export default CrawlerRequestPanel;
