/**
 * Worker表单组件
 * 用于创建和编辑Worker
 */

import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  InputNumber,
  Select,
  Button,
  Space,
  Card,
  Row,
  Col,
  message,
  Spin,
  Alert,
  Tooltip,
  Tag
} from 'antd';
import {
  SaveOutlined,
  CloseOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

import {
  workerApi,
  backendConfigApi,
  CrawlerWorker,
  CrawlerWorkerCreate,
  CrawlerWorkerUpdate,
  CrawlerConfigSummary,
  BackendConfigSummary,
  BackendConfigData
} from '../../../services';

const { TextArea } = Input;
const { Option } = Select;

interface WorkerFormProps {
  workerId?: string | null;
  crawlerConfigs: CrawlerConfigSummary[];
  backendConfigs: BackendConfigSummary[];
  onSuccess: () => void;
  onCancel: () => void;
}

const WorkerForm: React.FC<WorkerFormProps> = ({
  workerId,
  crawlerConfigs,
  backendConfigs,
  onSuccess,
  onCancel
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [, setWorkerData] = useState<CrawlerWorker | null>(null);
  const [selectedBackendConfig, setSelectedBackendConfig] = useState<BackendConfigData | null>(null);
  const [loadingBackendConfig, setLoadingBackendConfig] = useState(false);

  // 如果是编辑模式，加载Worker数据
  useEffect(() => {
    if (workerId) {
      loadWorkerData();
    } else {
      // 新建模式，设置默认值
      setDefaultValues();
    }
  }, [workerId]); // eslint-disable-line react-hooks/exhaustive-deps

  // 当后端配置变化时，在新建模式下重新设置默认值
  useEffect(() => {
    if (!workerId && selectedBackendConfig) {
      const currentValues = form.getFieldsValue();
      // 只有在表单值还是初始状态时才重新设置默认值
      if (!currentValues.max_concurrent_tasks || currentValues.max_concurrent_tasks <= 5) {
        setDefaultValues();
      }
    }
  }, [selectedBackendConfig, workerId]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadWorkerData = async () => {
    setLoading(true);
    try {
      const data = await workerApi.getWorker(workerId!);
      setWorkerData(data);
      
      // 设置表单值
      form.setFieldsValue({
        worker_name: data.worker_name,
        description: data.description,
        crawler_config_id: data.crawler_config_id,
        backend_config_id: data.backend_config_id,
        max_concurrent_tasks: data.max_concurrent_tasks,
        allocated_concurrent: data.allocated_concurrent,
        priority: data.priority,
        tags: data.tags,
      });
    } catch (error) {
      message.error(`加载Worker数据失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const setDefaultValues = () => {
    // 根据后端配置设置合理的默认值
    const maxConcurrent = selectedBackendConfig ? Math.min(5, selectedBackendConfig.max_concurrent) : 5;
    const allocatedConcurrent = Math.min(2, Math.max(1, maxConcurrent - 1));

    form.setFieldsValue({
      max_concurrent_tasks: maxConcurrent,
      allocated_concurrent: allocatedConcurrent,
      priority: 'normal',
      tags: [],
    });
  };

  // 获取后端配置详情
  const fetchBackendConfigDetails = async (backendId: string) => {
    if (!backendId) {
      setSelectedBackendConfig(null);
      return;
    }

    setLoadingBackendConfig(true);
    try {
      const config = await backendConfigApi.getConfig(backendId);
      setSelectedBackendConfig(config);
    } catch (error) {
      console.error('获取后端配置详情失败:', error);
      message.error('获取后端配置详情失败');
      setSelectedBackendConfig(null);
    } finally {
      setLoadingBackendConfig(false);
    }
  };

  // 从后端配置同步性能参数
  const syncPerformanceFromBackend = () => {
    if (!selectedBackendConfig) {
      message.warning('请先选择后端配置');
      return;
    }

    // 基于后端配置的性能级别和实际参数计算Worker配置
    let maxConcurrent = 5;
    let allocatedConcurrent = 2;

    // 根据性能级别设置基础值
    switch (selectedBackendConfig.performance_level) {
      case 'high':
        maxConcurrent = 10;
        allocatedConcurrent = 8;
        break;
      case 'medium':
        maxConcurrent = 5;
        allocatedConcurrent = 3;
        break;
      case 'low':
        maxConcurrent = 2;
        allocatedConcurrent = 1;
        break;
    }

    // 考虑后端配置的实际限制
    if (selectedBackendConfig.max_concurrent) {
      maxConcurrent = Math.min(maxConcurrent, selectedBackendConfig.max_concurrent);
    }

    // 考虑内存阈值的影响
    if (selectedBackendConfig.memory_threshold) {
      if (selectedBackendConfig.memory_threshold < 512) {
        // 低内存环境，减少并发
        maxConcurrent = Math.max(1, Math.floor(maxConcurrent * 0.6));
      } else if (selectedBackendConfig.memory_threshold > 2048) {
        // 高内存环境，可以增加并发
        maxConcurrent = Math.floor(maxConcurrent * 1.2);
      }
    }

    // 确保分配的并发数不超过最大并发数
    allocatedConcurrent = Math.min(allocatedConcurrent, Math.max(1, maxConcurrent - 1));

    form.setFieldsValue({
      max_concurrent_tasks: maxConcurrent,
      allocated_concurrent: allocatedConcurrent,
    });

    message.success(`已从后端配置同步性能参数 (最大并发: ${maxConcurrent}, 分配并发: ${allocatedConcurrent})`);
  };

  // 处理后端配置选择变化
  const handleBackendConfigChange = async (backendId: string) => {
    await fetchBackendConfigDetails(backendId);

    // 获取当前表单值
    const currentValues = form.getFieldsValue();

    // 获取新选择的后端配置
    const newBackendConfig = backendConfigs.find(config => config.backend_id === backendId);

    if (newBackendConfig) {
      // 检查当前最大并发任务数是否超过新后端的限制
      if (currentValues.max_concurrent_tasks > newBackendConfig.max_concurrent) {
        // 自动调整为后端限制值
        const newMaxConcurrent = newBackendConfig.max_concurrent;
        const newAllocatedConcurrent = Math.min(currentValues.allocated_concurrent || 1, Math.max(1, newMaxConcurrent - 1));

        form.setFieldsValue({
          max_concurrent_tasks: newMaxConcurrent,
          allocated_concurrent: newAllocatedConcurrent,
        });

        message.info(`已自动调整最大并发任务数为 ${newMaxConcurrent}（后端限制）`);
      }

      // 如果是新建模式且性能配置还是默认值，自动同步
      if (!workerId) {
        const isDefaultValues = currentValues.max_concurrent_tasks <= 5 && currentValues.allocated_concurrent <= 2;
        if (isDefaultValues) {
          // 延迟一下确保后端配置已加载
          setTimeout(() => {
            syncPerformanceFromBackend();
          }, 100);
        }
      }
    }
  };

  const handleSubmit = async (values: any) => {
    setSubmitting(true);
    try {
      const workerData: CrawlerWorkerCreate | CrawlerWorkerUpdate = {
        worker_name: values.worker_name,
        description: values.description,
        crawler_config_id: values.crawler_config_id,
        backend_config_id: values.backend_config_id,
        max_concurrent_tasks: values.max_concurrent_tasks,
        allocated_concurrent: values.allocated_concurrent,
        priority: values.priority,
        tags: values.tags || [],
      };

      if (workerId) {
        // 更新Worker
        await workerApi.updateWorker(workerId, workerData as CrawlerWorkerUpdate);
        message.success('更新Worker成功');
      } else {
        // 创建Worker
        await workerApi.createWorker(workerData as CrawlerWorkerCreate);
        message.success('创建Worker成功');
      }
      
      onSuccess();
    } catch (error) {
      message.error(`${workerId ? '更新' : '创建'}Worker失败: ${error}`);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  // 如果配置数据还没有加载完成，显示加载状态
  if (!crawlerConfigs || !backendConfigs) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>正在加载配置数据...</div>
      </div>
    );
  }

  // 获取可用的爬取配置
  const availableCrawlerConfigs = (crawlerConfigs || []).filter(config => config.status === 'active');

  // 获取可用的后端配置
  const availableBackendConfigs = (backendConfigs || []).filter(config => config.status === 'active');

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      scrollToFirstError
    >
      {/* 基本信息 */}
      <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="worker_name"
              label="Worker名称"
              rules={[{ required: true, message: '请输入Worker名称' }]}
            >
              <Input placeholder="请输入Worker名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="priority"
              label="优先级"
              rules={[{ required: true, message: '请选择优先级' }]}
            >
              <Select>
                <Option value="low">
                  <Tag color="default">低</Tag>
                </Option>
                <Option value="normal">
                  <Tag color="blue">普通</Tag>
                </Option>
                <Option value="high">
                  <Tag color="orange">高</Tag>
                </Option>
                <Option value="urgent">
                  <Tag color="red">紧急</Tag>
                </Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Form.Item
          name="description"
          label="描述"
        >
          <TextArea rows={2} placeholder="请输入Worker描述" />
        </Form.Item>
        <Form.Item
          name="tags"
          label="标签"
        >
          <Select
            mode="tags"
            placeholder="请输入标签"
            tokenSeparators={[',']}
          />
        </Form.Item>
      </Card>

      {/* 配置选择 */}
      <Card title="配置选择" size="small" style={{ marginBottom: 16 }}>
        <Alert
          message="配置选择"
          description="选择Worker使用的爬取配置和后端配置，只能选择活跃状态的配置"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Form.Item
          name="crawler_config_id"
          label={
            <span>
              爬取配置
              <Tooltip title="选择Worker使用的爬取配置，决定了爬取行为和参数">
                <InfoCircleOutlined style={{ marginLeft: 4 }} />
              </Tooltip>
            </span>
          }
          rules={[{ required: true, message: '请选择爬取配置' }]}
        >
          <Select
            placeholder="请选择爬取配置"
            showSearch
            optionFilterProp="children"
            filterOption={(input, option) =>
              (option?.children as any)?.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            {availableCrawlerConfigs.map(config => (
              <Option key={config.config_id} value={config.config_id}>
                <Space>
                  <span>{config.config_name}</span>
                  <Tag color="blue">v{config.version}</Tag>
                  {(config.tags || []).map(tag => (
                    <Tag key={tag}>{tag}</Tag>
                  ))}
                </Space>
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="backend_config_id"
          label={
            <span>
              后端配置
              <Tooltip title="选择Worker使用的后端配置，决定了爬取任务的执行环境">
                <InfoCircleOutlined style={{ marginLeft: 4 }} />
              </Tooltip>
            </span>
          }
          rules={[{ required: true, message: '请选择后端配置' }]}
        >
          <Select
            placeholder="请选择后端配置"
            showSearch
            optionFilterProp="children"
            onChange={handleBackendConfigChange}
            loading={loadingBackendConfig}
            filterOption={(input, option) =>
              (option?.children as any)?.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            {availableBackendConfigs.map(config => (
              <Option key={config.backend_id} value={config.backend_id}>
                <Space>
                  <span>{config.backend_name}</span>
                  <Tag color={config.performance_level === 'high' ? 'green' :
                              config.performance_level === 'medium' ? 'blue' : 'default'}>
                    {config.performance_level}
                  </Tag>
                  <Tag color={config.health_score >= 0.8 ? 'green' :
                              config.health_score >= 0.5 ? 'orange' : 'red'}>
                    {(config.health_score * 100).toFixed(0)}%
                  </Tag>
                </Space>
              </Option>
            ))}
          </Select>
        </Form.Item>
      </Card>

      {/* 性能配置 */}
      <Card
        title="性能配置"
        size="small"
        style={{ marginBottom: 16 }}
        extra={
          selectedBackendConfig && (
            <Button
              type="link"
              size="small"
              onClick={syncPerformanceFromBackend}
              loading={loadingBackendConfig}
            >
              从后端配置同步
            </Button>
          )
        }
      >
        {selectedBackendConfig && (
          <Alert
            message="后端配置信息"
            description={
              <Space direction="vertical" size="small">
                <div>
                  <strong>性能级别:</strong>
                  <Tag color={selectedBackendConfig.performance_level === 'high' ? 'green' :
                              selectedBackendConfig.performance_level === 'medium' ? 'blue' : 'default'}>
                    {selectedBackendConfig.performance_level}
                  </Tag>
                </div>
                <div>
                  <strong>最大并发:</strong> {selectedBackendConfig.max_concurrent}
                </div>
                <div>
                  <strong>内存阈值:</strong> {selectedBackendConfig.memory_threshold}MB
                </div>
                <div>
                  <strong>平均延迟:</strong> {selectedBackendConfig.mean_delay}ms
                </div>
              </Space>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="max_concurrent_tasks"
              label={
                <span>
                  最大并发任务数
                  <Tooltip title={`Worker可以同时处理的最大任务数量${selectedBackendConfig ? `（后端限制：${selectedBackendConfig.max_concurrent}）` : ''}`}>
                    <InfoCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                </span>
              }
              rules={[
                { required: true, message: '请输入最大并发任务数' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value) return Promise.resolve();
                    if (selectedBackendConfig && value > selectedBackendConfig.max_concurrent) {
                      return Promise.reject(new Error(`不能超过后端最大并发数 (${selectedBackendConfig.max_concurrent})`));
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <InputNumber
                min={1}
                max={selectedBackendConfig ? selectedBackendConfig.max_concurrent : 50}
                style={{ width: '100%' }}
                placeholder={selectedBackendConfig ? `最大: ${selectedBackendConfig.max_concurrent}` : '请先选择后端配置'}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="allocated_concurrent"
              label={
                <span>
                  分配并发数
                  <Tooltip title="实际分配给Worker的并发数，不能超过最大并发任务数">
                    <InfoCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                </span>
              }
              rules={[
                { required: true, message: '请输入分配并发数' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    const maxConcurrent = getFieldValue('max_concurrent_tasks');
                    if (!value || !maxConcurrent || value <= maxConcurrent) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('分配并发数不能超过最大并发任务数'));
                  },
                }),
              ]}
            >
              <InputNumber min={1} max={50} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      {/* 配置验证提示 */}
      {availableCrawlerConfigs.length === 0 && (
        <Alert
          message="没有可用的爬取配置"
          description="请先创建并激活至少一个爬取配置"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {availableBackendConfigs.length === 0 && (
        <Alert
          message="没有可用的后端配置"
          description="请先创建并激活至少一个后端配置"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 操作按钮 */}
      <div style={{ textAlign: 'right', marginTop: 24 }}>
        <Space>
          <Button onClick={onCancel}>
            <CloseOutlined /> 取消
          </Button>
          <Button 
            type="primary" 
            htmlType="submit" 
            loading={submitting}
            disabled={availableCrawlerConfigs.length === 0 || availableBackendConfigs.length === 0}
          >
            <SaveOutlined /> {workerId ? '更新' : '创建'}
          </Button>
        </Space>
      </div>
    </Form>
  );
};

export default WorkerForm;
