import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Card,
  Tabs,
  Button,
  Space,
  Tag,
  Statistic,
  Row,
  Col,
  Table,
  Typography,
  Spin,
  message,
  Modal,
  Input,
  Select,
  Tooltip,
  Alert,
  Descriptions,
  Empty,
  Checkbox,
  Divider
} from 'antd';
import { useRealTimeUpdates } from '../../hooks/useRealTimeUpdates';
import {
  ArrowLeftOutlined,
  PlayCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  DeleteOutlined,
  LinkOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  BarChartOutlined,
  FilterOutlined,
  PlusOutlined,
  TeamOutlined,
  PauseCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import {
  getMonitoringTask,
  getTaskUrls,
  getTaskExecutionHistory,
  getTaskStats,
  executeTaskManually,
  terminateTaskExecution,
  updateUrlsBatchStatus,
  updateUrlStatus,
  removeUrlsBatch,
  removeUrl,
  addUrlsToTask,
  type MonitoringTask,
  type TaskUrl
} from '../../services/monitoringTaskApi';
import { workerApi } from '../../services';
import type { CrawlerWorkerSummary, WorkerGroupCompatibilityCheck } from '../../services';
import { ExecutionHistoryTab, StatsTab } from './TaskDetailTabs';
import UrlPoolSelector from '../../components/UrlPoolSelector';
import { convertTimeForDisplay, getTimeFieldValue } from '../../utils/timeUtils';

const { Title, Text } = Typography;
const { Option } = Select;

// 扩展TaskUrl接口以包含Worker分配信息
interface ExtendedTaskUrl extends TaskUrl {
  assigned_worker_id?: string;
  processing_status?: 'pending' | 'processing' | 'completed' | 'failed' | 'skipped';
  last_processed_at?: string;
}

interface ExecutionHistory {
  id: string;
  start_time: string;
  end_time?: string;
  status: string;
  trigger: string;
  url_count: number;
  success_count: number;
  error_count: number;
  duration: number;
}

interface TaskStats {
  url_stats: {
    total_urls: number;
    active_urls: number;
    disabled_urls: number;
    error_urls: number;
  };
  execution_stats: {
    total_executions: number;
    successful_executions: number;
    failed_executions: number;
    success_rate: number;
    avg_duration: number;
  };
  task_info: {
    created_at: string;
    last_run?: string;
    next_run?: string;
    status: string;
    is_running: boolean;
  };
}

const TaskDetail: React.FC = () => {
  const { taskId } = useParams<{ taskId: string }>();
  const navigate = useNavigate();

  const [task, setTask] = useState<MonitoringTask | null>(null);

  // 实时更新Hook
  const {
    isConnected
  } = useRealTimeUpdates({
    taskId: taskId,
    onTaskProgress: (progress) => {
      console.log('Task progress updated:', progress);
      // 可以在这里更新本地状态或触发重新获取数据
    },
    onWorkerStatusChange: (status) => {
      console.log('Worker status updated:', status);
    },
    onUrlStatusChange: (status) => {
      console.log('URL status updated:', status);
      // 更新URL状态
      setTaskUrls((prevUrls: TaskUrl[]) =>
        prevUrls.map((url: TaskUrl) =>
          url.id === status.urlId
            ? { ...url, status: status.status as any, assigned_worker_id: status.workerId }
            : url
        )
      );
    }
  });
  const [taskUrls, setTaskUrls] = useState<TaskUrl[]>([]);
  const [executionHistory, setExecutionHistory] = useState<ExecutionHistory[]>([]);
  const [taskStats, setTaskStats] = useState<TaskStats | null>(null);
  const [taskAssignments, setTaskAssignments] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('basic');
  const [executing, setExecuting] = useState(false);
  const [scheduleLoading, setScheduleLoading] = useState(false);

  // Worker指派相关状态
  const [workerAssignModalVisible, setWorkerAssignModalVisible] = useState(false);
  const [availableWorkers, setAvailableWorkers] = useState<CrawlerWorkerSummary[]>([]);
  const [selectedWorkers, setSelectedWorkers] = useState<string[]>([]);
  const [assignmentLoading, setAssignmentLoading] = useState(false);
  const [compatibilityCheck, setCompatibilityCheck] = useState<WorkerGroupCompatibilityCheck | null>(null);

  // 获取任务详情
  const fetchTaskDetail = async () => {
    if (!taskId) return;
    
    try {
      setLoading(true);
      const response = await getMonitoringTask(taskId);
      if (response.success) {
        setTask(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch task detail:', error);
      message.error('获取任务详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取任务URL列表
  const fetchTaskUrls = async () => {
    if (!taskId) return;

    try {
      // 分页获取所有URL
      let allUrls: TaskUrl[] = [];
      let page = 1;
      const pageSize = 100; // 使用最大允许的页面大小
      let hasMore = true;

      while (hasMore) {
        const response = await getTaskUrls(taskId, {
          page: page,
          page_size: pageSize
        });

        if (response.success) {
          allUrls.push(...response.data);

          // 检查是否还有更多页面
          hasMore = response.data.length === pageSize;
          page++;
        } else {
          hasMore = false;
        }
      }

      setTaskUrls(allUrls);
      console.log(`Loaded ${allUrls.length} URLs for task ${taskId} across ${page - 1} pages`);
    } catch (error) {
      console.error('Failed to fetch task URLs:', error);
      message.error('获取任务URL失败');
    }
  };

  // 获取执行历史
  const fetchExecutionHistory = async () => {
    if (!taskId) return;
    
    try {
      const response = await getTaskExecutionHistory(taskId);
      if (response.success) {
        setExecutionHistory(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch execution history:', error);
      message.error('获取执行历史失败');
    }
  };

  // 获取统计信息
  const fetchTaskStats = async () => {
    if (!taskId) return;

    try {
      const response = await getTaskStats(taskId);
      if (response.success) {
        setTaskStats(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch task stats:', error);
      message.error('获取统计信息失败');
    }
  };

  // 获取任务分配信息
  const fetchTaskAssignments = async () => {
    if (!taskId) return;

    try {
      const assignments = await workerApi.getTaskAssignments(taskId);
      setTaskAssignments(assignments);
    } catch (error) {
      console.error('Failed to fetch task assignments:', error);
      message.error('获取任务分配信息失败');
    }
  };

  // 手动执行任务
  const handleExecuteTask = async () => {
    if (!taskId) return;
    
    try {
      setExecuting(true);
      const response = await executeTaskManually(taskId);
      if (response.success) {
        message.success('任务执行已启动');
        await fetchTaskDetail();
        await fetchExecutionHistory();
      }
    } catch (error) {
      console.error('Failed to execute task:', error);
      message.error('执行任务失败');
    } finally {
      setExecuting(false);
    }
  };

  // 终止任务执行
  const handleTerminateTask = async () => {
    if (!taskId) return;

    Modal.confirm({
      title: '确认终止任务',
      content: '确定要终止正在执行的任务吗？',
      onOk: async () => {
        try {
          const response = await terminateTaskExecution(taskId);
          if (response.success) {
            message.success('任务已终止');
            await fetchTaskDetail();
            await fetchExecutionHistory();
          }
        } catch (error) {
          console.error('Failed to terminate task:', error);
          message.error('终止任务失败');
        }
      }
    });
  };

  // 启用/禁用调度
  const handleToggleSchedule = async (enabled: boolean) => {
    if (!taskId) return;

    try {
      setScheduleLoading(true);
      const response = await fetch(`/api/v1/monitoring/tasks/${taskId}/schedule`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ enabled })
      });

      if (response.ok) {
        message.success(`调度已${enabled ? '启用' : '禁用'}`);
        fetchTaskDetail();
      } else {
        throw new Error('Failed to toggle schedule');
      }
    } catch (error) {
      console.error('Failed to toggle schedule:', error);
      message.error(`${enabled ? '启用' : '禁用'}调度失败`);
    } finally {
      setScheduleLoading(false);
    }
  };

  // 编辑调度配置
  const handleEditSchedule = () => {
    // TODO: 实现调度配置编辑功能
    message.info('调度配置编辑功能开发中...');
  };

  // 获取可用的Worker列表
  const fetchAvailableWorkers = async () => {
    try {
      const workers = await workerApi.getWorkers();
      setAvailableWorkers(workers.filter(worker => worker.status === 'active'));
    } catch (error) {
      console.error('Failed to fetch workers:', error);
      message.error('获取Worker列表失败');
    }
  };

  // 打开Worker指派模态框
  const handleAssignWorkers = async () => {
    setWorkerAssignModalVisible(true);
    await fetchAvailableWorkers();

    // 获取当前任务已指派的Worker ID列表
    const assignedWorkerIds: string[] = [];

    // 从活跃的任务分配中获取Worker ID
    const activeAssignments = taskAssignments.filter(assignment =>
      assignment.status !== 'cancelled' && assignment.status !== 'failed'
    );

    // 直接从taskAssignments获取worker_ids（现在TaskAssignmentSummary包含这个字段）
    for (const assignment of activeAssignments) {
      if (assignment.worker_ids && assignment.worker_ids.length > 0) {
        assignedWorkerIds.push(...assignment.worker_ids);
      }
    }

    // 去重
    const uniqueAssignedWorkerIds = assignedWorkerIds.filter((id, index) => assignedWorkerIds.indexOf(id) === index);

    // 预选已指派的Worker
    setSelectedWorkers(uniqueAssignedWorkerIds);

    // 如果有已选择的Worker，检查兼容性
    if (uniqueAssignedWorkerIds.length > 0) {
      checkWorkerCompatibility(uniqueAssignedWorkerIds);
    }
  };

  // 检查Worker兼容性
  const checkWorkerCompatibility = async (workerIds: string[]) => {
    if (workerIds.length === 0) {
      setCompatibilityCheck(null);
      return;
    }

    try {
      // 检查Worker组兼容性
      const result = await workerApi.checkGroupCompatibility(workerIds);
      setCompatibilityCheck(result);
    } catch (error) {
      console.error('Failed to check worker compatibility:', error);
      message.error('兼容性检查失败');
    }
  };

  // 处理Worker选择变化
  const handleWorkerSelectionChange = (workerIds: string[]) => {
    setSelectedWorkers(workerIds);
    checkWorkerCompatibility(workerIds);
  };

  // 确认指派Worker
  const handleConfirmAssignment = async () => {
    if (!taskId || selectedWorkers.length === 0) {
      message.warning('请选择至少一个Worker');
      return;
    }

    // 计算已经指派的Worker ID列表
    const alreadyAssignedWorkerIds: string[] = [];
    const activeAssignments = taskAssignments.filter(assignment =>
      assignment.status !== 'cancelled' && assignment.status !== 'failed'
    );

    for (const assignment of activeAssignments) {
      if (assignment.worker_ids && assignment.worker_ids.length > 0) {
        alreadyAssignedWorkerIds.push(...assignment.worker_ids);
      }
    }

    // 计算新增的Worker（只指派这些Worker）
    const newWorkerIds = selectedWorkers.filter(workerId =>
      !alreadyAssignedWorkerIds.includes(workerId)
    );

    if (newWorkerIds.length === 0) {
      message.info('所有选中的Worker都已经指派给此任务');
      return;
    }

    setAssignmentLoading(true);
    try {
      // 只为新增的Worker创建分配
      const assignmentData = {
        task_id: taskId,
        worker_ids: newWorkerIds,
        assignment_strategy: 'health_based',
        auto_failover: true,
        assignment_name: `${task?.name || '监控任务'} - Worker分配`,
        description: `为任务 ${task?.name} 新增分配 ${newWorkerIds.length} 个Worker`
      };

      await workerApi.createTaskAssignment(assignmentData);
      message.success(`成功指派 ${newWorkerIds.length} 个新Worker`);
      setWorkerAssignModalVisible(false);
      setSelectedWorkers([]);
      setCompatibilityCheck(null);

      // 刷新任务详情和Worker分配信息
      await Promise.all([
        fetchTaskDetail(),
        fetchTaskAssignments()
      ]);
    } catch (error: any) {
      console.error('Failed to assign workers:', error);
      message.error(`Worker分配失败: ${error.message}`);
    } finally {
      setAssignmentLoading(false);
    }
  };

  // 渲染兼容性检查结果
  const renderCompatibilityCheck = () => {
    if (!compatibilityCheck) return null;

    return (
      <div style={{ marginTop: 16 }}>
        <Divider>兼容性检查结果</Divider>
        <Alert
          type={compatibilityCheck.is_compatible ? 'success' : 'error'}
          message={
            compatibilityCheck.is_compatible
              ? '✅ Worker组合兼容，可以安全指派'
              : '❌ Worker组合存在冲突，不建议指派'
          }
          description={
            <div>
              {/* 显示个别Worker的错误 */}
              {compatibilityCheck.individual_checks.some(check => check.errors.length > 0) && (
                <div style={{ marginBottom: 8 }}>
                  <Text type="danger">错误：</Text>
                  <ul style={{ margin: 0, paddingLeft: 20 }}>
                    {compatibilityCheck.individual_checks.map((check, checkIndex) =>
                      check.errors.map((error: string, errorIndex: number) => (
                        <li key={`${checkIndex}-${errorIndex}`}>
                          Worker {check.worker_id}: {error}
                        </li>
                      ))
                    )}
                  </ul>
                </div>
              )}

              {/* 显示个别Worker的警告 */}
              {compatibilityCheck.individual_checks.some(check => check.warnings.length > 0) && (
                <div style={{ marginBottom: 8 }}>
                  <Text type="warning">警告：</Text>
                  <ul style={{ margin: 0, paddingLeft: 20 }}>
                    {compatibilityCheck.individual_checks.map((check, checkIndex) =>
                      check.warnings.map((warning: string, warningIndex: number) => (
                        <li key={`${checkIndex}-${warningIndex}`}>
                          Worker {check.worker_id}: {warning}
                        </li>
                      ))
                    )}
                  </ul>
                </div>
              )}

              {/* 显示资源冲突 */}
              {compatibilityCheck.resource_conflicts.length > 0 && (
                <div style={{ marginBottom: 8 }}>
                  <Text type="danger">资源冲突：</Text>
                  <ul style={{ margin: 0, paddingLeft: 20 }}>
                    {compatibilityCheck.resource_conflicts.map((conflict, index) => (
                      <li key={index}>
                        {conflict.type}: {conflict.workers?.join(', ')}
                        {conflict.required_concurrent && conflict.max_concurrent &&
                          ` (需要: ${conflict.required_concurrent}, 最大: ${conflict.max_concurrent})`
                        }
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* 显示统计信息 */}
              <div style={{ marginBottom: 8 }}>
                <Text type="secondary">
                  统计: 总计 {compatibilityCheck.total_workers} 个Worker,
                  有效 {compatibilityCheck.valid_workers} 个,
                  冲突 {compatibilityCheck.conflicted_workers} 个
                </Text>
              </div>

              {/* 显示推荐Worker */}
              {compatibilityCheck.recommended_workers.length > 0 && (
                <div>
                  <Text type="success">推荐Worker: {compatibilityCheck.recommended_workers.join(', ')}</Text>
                </div>
              )}
            </div>
          }
          showIcon
        />
      </div>
    );
  };

  useEffect(() => {
    fetchTaskDetail();
    fetchTaskUrls();
    fetchExecutionHistory();
    fetchTaskStats();
    fetchTaskAssignments();
  }, [taskId]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载任务详情中...</div>
      </div>
    );
  }

  if (!task) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Empty description="任务不存在" />
        <Button type="primary" onClick={() => navigate('/monitoring')}>
          返回任务列表
        </Button>
      </div>
    );
  }

  // 状态配置
  const statusConfig = {
    active: { color: 'green', text: '运行中' },
    paused: { color: 'orange', text: '已暂停' },
    stopped: { color: 'red', text: '已停止' },
    draft: { color: 'default', text: '草稿' }
  };

  return (
    <div className="task-detail">
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/monitoring')}
          >
            返回
          </Button>
          <Title level={2} style={{ margin: 0 }}>
            {task.name}
          </Title>
          <Tag color={statusConfig[task.status]?.color}>
            {statusConfig[task.status]?.text}
          </Tag>
          {task.is_running && (
            <Tag color="blue" icon={<ClockCircleOutlined />}>
              执行中
            </Tag>
          )}

          {/* 实时连接状态指示器 */}
          <Tag
            color={isConnected ? 'green' : 'red'}
            style={{ marginLeft: 'auto' }}
          >
            {isConnected ? '🟢 实时连接' : '🔴 连接断开'}
          </Tag>
        </Space>
        
        <div style={{ marginTop: 16 }}>
          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={handleExecuteTask}
              loading={executing}
              disabled={task.is_running}
            >
              立即执行
            </Button>
            {task.is_running && (
              <Button
                danger
                icon={<StopOutlined />}
                onClick={handleTerminateTask}
              >
                终止执行
              </Button>
            )}

            <Button
              type="default"
              icon={<TeamOutlined />}
              onClick={handleAssignWorkers}
            >
              指派Worker
            </Button>

            {/* 调度控制按钮 */}
            {task.schedule && (
              <>
                <Button
                  type={task.schedule.enabled ? "default" : "primary"}
                  icon={task.schedule.enabled ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                  onClick={() => handleToggleSchedule(!task.schedule.enabled)}
                  loading={scheduleLoading}
                >
                  {task.schedule.enabled ? '禁用调度' : '启用调度'}
                </Button>
                <Button
                  icon={<SettingOutlined />}
                  onClick={handleEditSchedule}
                >
                  调度配置
                </Button>
              </>
            )}

            <Button icon={<ReloadOutlined />} onClick={fetchTaskDetail}>
              刷新
            </Button>
          </Space>
        </div>
      </div>

      {/* 任务详情内容 */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'basic',
            label: (
              <span>
                <CheckCircleOutlined />
                任务配置
              </span>
            ),
            children: <BasicInfoTab task={task} taskStats={taskStats} />
          },
          {
            key: 'urls',
            label: (
              <span>
                <LinkOutlined />
                URL管理
              </span>
            ),
            children: (
              <UrlManagementTab
                taskId={taskId!}
                taskUrls={taskUrls}
                onRefresh={fetchTaskUrls}
                onRefreshStats={fetchTaskStats}
              />
            )
          },
          {
            key: 'history',
            label: (
              <span>
                <ClockCircleOutlined />
                执行历史
              </span>
            ),
            children: (
              <ExecutionHistoryTab
                executionHistory={executionHistory}
                onRefresh={fetchExecutionHistory}
              />
            )
          },
          {
            key: 'stats',
            label: (
              <span>
                <BarChartOutlined />
                统计图表
              </span>
            ),
            children: <StatsTab taskStats={taskStats} />
          },
          {
            key: 'assignments',
            label: (
              <span>
                <TeamOutlined />
                Worker分配
              </span>
            ),
            children: (
              <WorkerAssignmentTab
                taskId={taskId!}
                taskAssignments={taskAssignments}
                taskUrls={taskUrls as ExtendedTaskUrl[]}
                onRefresh={fetchTaskAssignments}
              />
            )
          }
        ]}
      />

      {/* Worker指派模态框 */}
      <Modal
        title="指派Worker"
        open={workerAssignModalVisible}
        onCancel={() => {
          setWorkerAssignModalVisible(false);
          setSelectedWorkers([]);
          setCompatibilityCheck(null);
        }}
        onOk={handleConfirmAssignment}
        confirmLoading={assignmentLoading}
        width={800}
        okText="确认指派"
        cancelText="取消"
        okButtonProps={{
          disabled: selectedWorkers.length === 0 || (compatibilityCheck ? !compatibilityCheck.is_compatible : false)
        }}
      >
        <div style={{ marginBottom: 16 }}>
          <Text type="secondary">
            为任务 "{task?.name}" 选择要指派的Worker。系统会自动检查Worker之间的兼容性和资源冲突。
          </Text>
        </div>

        {/* Worker选择列表 */}
        <div style={{ marginBottom: 16 }}>
          <Text strong>可用Worker列表：</Text>
          <Checkbox.Group
            value={selectedWorkers}
            onChange={handleWorkerSelectionChange}
            style={{ width: '100%', marginTop: 8 }}
          >
            <Row gutter={[16, 16]}>
              {availableWorkers.map((worker: CrawlerWorkerSummary) => {
                // 检查Worker是否已指派给当前任务（通过selectedWorkers判断，因为在打开模态框时已经预选了已指派的Worker）
                const isAssigned = selectedWorkers.includes(worker.worker_id);

                return (
                  <Col span={12} key={worker.worker_id}>
                    <Card
                      size="small"
                      style={{
                        height: '100%',
                        border: isAssigned ? '2px solid #1890ff' : undefined,
                        backgroundColor: isAssigned ? '#f6ffed' : undefined
                      }}
                    >
                      <Checkbox value={worker.worker_id} style={{ marginBottom: 8 }}>
                        <Text strong>{worker.worker_name}</Text>
                        {isAssigned && (
                          <Tag color="blue" style={{ marginLeft: 8 }}>
                            已指派
                          </Tag>
                        )}
                      </Checkbox>
                      <div>
                        <Tag color={worker.status === 'active' ? 'green' : 'orange'}>
                          {worker.status}
                        </Tag>
                        <Tag color={worker.priority === 'high' ? 'red' : worker.priority === 'normal' ? 'blue' : 'default'}>
                          {worker.priority}
                        </Tag>
                      </div>
                      <div style={{ marginTop: 4, fontSize: '12px', color: '#666' }}>
                        负载: {worker.current_tasks}/{worker.allocated_concurrent} |
                        成功率: {(worker.success_rate * 100).toFixed(1)}%
                      </div>
                    </Card>
                  </Col>
                );
              })}
            </Row>
          </Checkbox.Group>
        </div>

        {/* 兼容性检查结果 */}
        {renderCompatibilityCheck()}
      </Modal>
    </div>
  );
};

// 基本信息Tab组件
const BasicInfoTab: React.FC<{
  task: MonitoringTask;
  taskStats: TaskStats | null;
}> = ({ task, taskStats }) => {
  // 格式化时间显示
  const formatDateTime = (dateTime: string) => {
    try {
      const date = new Date(dateTime);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      console.warn('Failed to format date time:', dateTime, error);
      return dateTime;
    }
  };

  // 格式化时间显示（用于HH:mm格式的时间）
  const formatTimeOnly = (timeStr: string) => {
    // 如果是HH:mm格式，直接返回
    if (/^\d{2}:\d{2}$/.test(timeStr)) {
      return timeStr;
    }
    // 如果是完整的日期时间，提取时间部分
    try {
      const date = new Date(timeStr);
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    } catch (error) {
      console.warn('Failed to format time:', timeStr, error);
      return timeStr;
    }
  };

  // 获取优先级显示配置
  const getPriorityConfig = (priority: string) => {
    const configs: Record<string, { color: string; text: string }> = {
      low: { color: '#52c41a', text: '低优先级' },
      normal: { color: '#1890ff', text: '普通优先级' },
      medium: { color: '#1890ff', text: '中等优先级' },
      high: { color: '#fa8c16', text: '高优先级' },
      urgent: { color: '#f5222d', text: '紧急优先级' }
    };
    return configs[priority] || { color: '#1890ff', text: priority };
  };

  // 获取调度类型显示文本
  const getScheduleTypeText = (type: string) => {
    const types: Record<string, string> = {
      once: '单次执行',
      daily: '每日执行',
      weekly: '每周执行',
      hourly: '每小时执行',
      custom: '自定义调度'
    };
    return types[type] || type;
  };

  const priorityConfig = getPriorityConfig(task.config.priority);

  return (
    <Row gutter={[24, 24]}>
      {/* 基本信息卡片 */}
      <Col span={24}>
        <Card title="基本信息" size="small">
          <Descriptions column={2} bordered size="small">
            <Descriptions.Item label="任务ID">{task.id}</Descriptions.Item>
            <Descriptions.Item label="任务名称">{task.name}</Descriptions.Item>
            <Descriptions.Item label="任务状态">
              <Tag color={task.status === 'active' ? 'green' : 'orange'}>
                {task.status === 'active' ? '运行中' : '已暂停'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="是否正在执行">
              {task.is_running ? (
                <Tag color="blue" icon={<ClockCircleOutlined />}>执行中</Tag>
              ) : (
                <Tag color="default">空闲</Tag>
              )}
            </Descriptions.Item>
            <Descriptions.Item label="任务描述" span={2}>
              {task.description || '无描述'}
            </Descriptions.Item>
            <Descriptions.Item label="任务标签" span={2}>
              {task.tags && task.tags.length > 0 ? (
                <Space wrap>
                  {task.tags.map((tag, index) => (
                    <Tag key={index} color="blue">{tag}</Tag>
                  ))}
                </Space>
              ) : '无标签'}
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">{formatDateTime(task.created_at)}</Descriptions.Item>
            <Descriptions.Item label="更新时间">{formatDateTime(task.updated_at)}</Descriptions.Item>
            <Descriptions.Item label="上次执行">{task.last_run ? formatDateTime(task.last_run) : '未执行'}</Descriptions.Item>
            <Descriptions.Item label="下次执行">
              <div>
                {task.next_run ? formatDateTime(task.next_run) : '未安排'}
                {task.next_run && task.schedule.enable_random_delay && (
                  <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                    <span style={{ marginRight: '4px' }}>ℹ️</span>
                    已应用随机延迟 ({task.schedule.random_delay_min || 0}-{task.schedule.random_delay_max || 0}分钟)
                  </div>
                )}
                {task.next_run && task.schedule.time && (
                  <div style={{ fontSize: '12px', color: '#999', marginTop: '2px' }}>
                    基础执行时间: {task.schedule.time}
                  </div>
                )}
              </div>
            </Descriptions.Item>
          </Descriptions>
        </Card>
      </Col>

      {/* 调度配置卡片 */}
      <Col span={12}>
        <Card
          title="调度配置"
          size="small"
          extra={
            <Space>
              <Button
                type="text"
                size="small"
                icon={task.schedule.enabled ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={() => handleToggleSchedule(!task.schedule.enabled)}
                loading={scheduleLoading}
              >
                {task.schedule.enabled ? '禁用' : '启用'}
              </Button>
              <Button
                type="text"
                size="small"
                icon={<SettingOutlined />}
                onClick={handleEditSchedule}
              >
                编辑
              </Button>
            </Space>
          }
        >
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item label="调度类型">
              <Tag color="purple">{getScheduleTypeText(task.schedule.type)}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="调度状态">
              <Tag color={task.schedule.enabled ? 'green' : 'red'}>
                {task.schedule.enabled ? '已启用' : '已禁用'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="执行时间">
              {(() => {
                const timeValue = getTimeFieldValue(task.schedule);
                return convertTimeForDisplay(timeValue, task.schedule.type);
              })()}
            </Descriptions.Item>
            <Descriptions.Item label="时区">
              {task.schedule.timezone}
            </Descriptions.Item>
            {task.schedule.days && task.schedule.days.length > 0 ? (
              <Descriptions.Item label="执行日期">
                <Space wrap>
                  {task.schedule.days.map(day => (
                    <Tag key={day} color="geekblue">
                      {['周日', '周一', '周二', '周三', '周四', '周五', '周六'][day]}
                    </Tag>
                  ))}
                </Space>
              </Descriptions.Item>
            ) : null}
            {task.schedule.interval ? (
              <Descriptions.Item label="间隔时间">
                {task.schedule.interval} 分钟
              </Descriptions.Item>
            ) : null}
            {task.schedule.cron_expression ? (
              <Descriptions.Item label="Cron表达式">
                <Text code>{task.schedule.cron_expression}</Text>
              </Descriptions.Item>
            ) : null}
            {task.schedule.end_time ? (
              <Descriptions.Item label="结束时间">
                {convertTimeForDisplay(task.schedule.end_time, 'daily')}
              </Descriptions.Item>
            ) : null}
            {task.schedule.max_runs ? (
              <Descriptions.Item label="最大执行次数">
                {task.schedule.max_runs}
              </Descriptions.Item>
            ) : null}
            {process.env.NODE_ENV === 'development' && (
              <Descriptions.Item label="调试信息">
                <div style={{ fontSize: '12px', fontFamily: 'monospace', color: '#666' }}>
                  <div>设置时间: {task.schedule.time || '未设置'}</div>
                  <div>时区: {task.schedule.timezone || '未设置'}</div>
                  <div>随机延迟: {task.schedule.enable_random_delay ? 'Yes' : 'No'}</div>
                  {task.schedule.enable_random_delay && (
                    <div>延迟范围: {task.schedule.random_delay_min || 0}-{task.schedule.random_delay_max || 0}分钟</div>
                  )}
                  <div>存储时间: {task.next_run || '未设置'}</div>
                </div>
              </Descriptions.Item>
            )}
            {task.schedule.enable_random_delay && (
              <Descriptions.Item label="随机延迟">
                <Tag color="orange">已启用</Tag>
                <div style={{ marginTop: 4, fontSize: '12px', color: '#666' }}>
                  延迟范围: {task.schedule.random_delay_min || 0} - {task.schedule.random_delay_max || 180} 分钟
                </div>
              </Descriptions.Item>
            )}
          </Descriptions>
        </Card>
      </Col>

      {/* 任务配置卡片 */}
      <Col span={12}>
        <Card title="任务配置" size="small">
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item label="目标平台">
              <Tag color="gold">{task.config.platform}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="任务优先级">
              <Tag color={priorityConfig.color}>{priorityConfig.text}</Tag>
            </Descriptions.Item>

            <Descriptions.Item label="通知设置">
              <Tag color={task.config.enable_notifications ? 'green' : 'red'}>
                {task.config.enable_notifications ? '已启用' : '已禁用'}
              </Tag>
            </Descriptions.Item>
            {task.config.notification_config && (
              <Descriptions.Item label="通知配置">
                <Text code style={{ fontSize: '12px' }}>
                  {JSON.stringify(task.config.notification_config, null, 2)}
                </Text>
              </Descriptions.Item>
            )}
          </Descriptions>
        </Card>
      </Col>

      {/* 任务统计信息 */}
      {taskStats && (
        <Col span={24}>
          <Card title="任务统计信息" size="small">
            <Row gutter={[16, 16]}>
              {/* URL统计 */}
              <Col span={6}>
                <Card size="small" style={{ textAlign: 'center', backgroundColor: '#f6ffed' }}>
                  <Statistic
                    title="总URL数"
                    value={taskStats.url_stats.total_urls}
                    prefix={<LinkOutlined style={{ color: '#52c41a' }} />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" style={{ textAlign: 'center', backgroundColor: '#f6ffed' }}>
                  <Statistic
                    title="活跃URL"
                    value={taskStats.url_stats.active_urls}
                    prefix={<CheckCircleOutlined style={{ color: '#3f8600' }} />}
                    valueStyle={{ color: '#3f8600' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" style={{ textAlign: 'center', backgroundColor: '#fff2e8' }}>
                  <Statistic
                    title="禁用URL"
                    value={taskStats.url_stats.total_urls - taskStats.url_stats.active_urls}
                    prefix={<ExclamationCircleOutlined style={{ color: '#fa8c16' }} />}
                    valueStyle={{ color: '#fa8c16' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" style={{ textAlign: 'center', backgroundColor: '#e6f7ff' }}>
                  <Statistic
                    title="URL活跃率"
                    value={taskStats.url_stats.total_urls > 0 ?
                      Math.round((taskStats.url_stats.active_urls / taskStats.url_stats.total_urls) * 100) : 0}
                    suffix="%"
                    prefix={<BarChartOutlined style={{ color: '#1890ff' }} />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>

              {/* 执行统计 */}
              <Col span={6}>
                <Card size="small" style={{ textAlign: 'center', backgroundColor: '#f0f5ff' }}>
                  <Statistic
                    title="总执行次数"
                    value={taskStats.execution_stats.total_executions}
                    prefix={<PlayCircleOutlined style={{ color: '#722ed1' }} />}
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" style={{ textAlign: 'center', backgroundColor: '#f6ffed' }}>
                  <Statistic
                    title="成功次数"
                    value={taskStats.execution_stats.successful_executions || 0}
                    prefix={<CheckCircleOutlined style={{ color: '#3f8600' }} />}
                    valueStyle={{ color: '#3f8600' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" style={{ textAlign: 'center', backgroundColor: '#fff1f0' }}>
                  <Statistic
                    title="失败次数"
                    value={taskStats.execution_stats.failed_executions || 0}
                    prefix={<ExclamationCircleOutlined style={{ color: '#cf1322' }} />}
                    valueStyle={{ color: '#cf1322' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" style={{ textAlign: 'center', backgroundColor:
                  taskStats.execution_stats.success_rate > 80 ? '#f6ffed' : '#fff1f0' }}>
                  <Statistic
                    title="成功率"
                    value={taskStats.execution_stats.success_rate}
                    suffix="%"
                    prefix={taskStats.execution_stats.success_rate > 80 ?
                      <CheckCircleOutlined style={{ color: '#3f8600' }} /> :
                      <ExclamationCircleOutlined style={{ color: '#cf1322' }} />}
                    valueStyle={{
                      color: taskStats.execution_stats.success_rate > 80 ? '#3f8600' : '#cf1322'
                    }}
                  />
                </Card>
              </Col>

              {/* 性能统计 */}
              {taskStats.execution_stats.avg_duration ? (
                <Col span={6}>
                  <Card size="small" style={{ textAlign: 'center', backgroundColor: '#f9f0ff' }}>
                    <Statistic
                      title="平均执行时间"
                      value={taskStats.execution_stats.avg_duration}
                      suffix="秒"
                      prefix={<ClockCircleOutlined style={{ color: '#722ed1' }} />}
                      valueStyle={{ color: '#722ed1' }}
                    />
                  </Card>
                </Col>
              ) : null}

              {task.last_run && (
                <Col span={6}>
                  <Card size="small" style={{ textAlign: 'center', backgroundColor: '#f6ffed' }}>
                    <div style={{ padding: '8px 0' }}>
                      <div style={{ fontSize: '14px', color: '#666', marginBottom: '4px' }}>
                        最后执行时间
                      </div>
                      <div style={{ fontSize: '12px', color: '#3f8600', fontWeight: 'bold' }}>
                        {formatDateTime(task.last_run)}
                      </div>
                    </div>
                  </Card>
                </Col>
              )}
            </Row>
          </Card>
        </Col>
      )}
    </Row>
  );
};

// URL管理Tab组件
const UrlManagementTab: React.FC<{
  taskId: string;
  taskUrls: TaskUrl[];
  onRefresh: () => void;
  onRefreshStats: () => void;
}> = ({ taskId, taskUrls, onRefresh, onRefreshStats }) => {
  const [selectedUrls, setSelectedUrls] = useState<React.Key[]>([]);
  const [filteredUrls, setFilteredUrls] = useState<TaskUrl[]>(taskUrls);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    platform: undefined as string | undefined,
    status: undefined as string | undefined,
    source_file: undefined as string | undefined,
    search: undefined as string | undefined,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [showUrlPoolModal, setShowUrlPoolModal] = useState(false);

  // 获取平台列表和来源文件列表
  const platforms = Array.from(new Set(taskUrls.map(url => url.platform).filter(Boolean)));
  const sourceFiles = Array.from(new Set(taskUrls.map(url => url.source_file).filter(Boolean)));

  // 应用筛选
  useEffect(() => {
    let filtered = [...taskUrls];

    // 平台筛选
    if (filters.platform) {
      filtered = filtered.filter(url => url.platform === filters.platform);
    }

    // 状态筛选
    if (filters.status) {
      filtered = filtered.filter(url => url.status === filters.status);
    }

    // 来源文件筛选
    if (filters.source_file) {
      filtered = filtered.filter(url => url.source_file === filters.source_file);
    }

    // 搜索筛选
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(url =>
        url.url.toLowerCase().includes(searchLower)
      );
    }

    setFilteredUrls(filtered);
    setPagination(prev => ({
      ...prev,
      total: filtered.length,
      current: 1, // 重置到第一页
    }));

    // 清理无效的选择状态（筛选后不存在的URL）
    const filteredUrlIds = new Set(filtered.map(url => url.id));
    setSelectedUrls(prev => prev.filter(id => filteredUrlIds.has(id as string)));
  }, [taskUrls, filters]);

  // 更新筛选条件
  const updateFilter = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // 清空筛选
  const clearFilters = () => {
    setFilters({
      platform: undefined,
      status: undefined,
      source_file: undefined,
      search: undefined,
    });
  };

  // 获取当前页的数据
  const getCurrentPageData = () => {
    const start = (pagination.current - 1) * pagination.pageSize;
    const end = start + pagination.pageSize;
    return filteredUrls.slice(start, end);
  };

  // 处理分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize,
    }));
  };

  // 批量操作
  const handleBatchEnable = async () => {
    if (selectedUrls.length === 0) {
      message.warning('请先选择要启用的URL');
      return;
    }

    try {
      // 验证选中的URL是否都存在于任务中
      const validUrlIds = selectedUrls.filter(urlId =>
        taskUrls.some(url => url.id === urlId)
      );

      if (validUrlIds.length === 0) {
        message.error('选中的URL无效，请重新选择');
        setSelectedUrls([]);
        return;
      }

      console.log(`Batch enabling ${validUrlIds.length} URLs for task ${taskId}:`, validUrlIds);

      const response = await updateUrlsBatchStatus(taskId, validUrlIds as string[], 'active');
      console.log('Batch enable response:', response);

      if (response.success) {
        message.success(response.message);
        if (response.failed_urls.length > 0) {
          console.warn('Failed URLs:', response.failed_urls);
          message.warning(`部分URL操作失败，请查看控制台了解详情`);
        }
      } else {
        console.error('Batch enable failed:', response);
        message.error(`批量启用失败: ${response.message || '未知错误'}`);
      }
      setSelectedUrls([]);
      // 刷新URL列表和统计信息
      onRefresh();
      onRefreshStats();
    } catch (error: any) {
      console.error('Batch enable error:', error);
      message.error(`批量启用失败: ${error.message}`);
    }
  };

  const handleBatchDisable = async () => {
    if (selectedUrls.length === 0) {
      message.warning('请先选择要禁用的URL');
      return;
    }

    try {
      // 验证选中的URL是否都存在于任务中
      const validUrlIds = selectedUrls.filter(urlId =>
        taskUrls.some(url => url.id === urlId)
      );

      if (validUrlIds.length === 0) {
        message.error('选中的URL无效，请重新选择');
        setSelectedUrls([]);
        return;
      }

      console.log(`Batch disabling ${validUrlIds.length} URLs for task ${taskId}:`, validUrlIds);

      const response = await updateUrlsBatchStatus(taskId, validUrlIds as string[], 'disabled');
      console.log('Batch disable response:', response);

      if (response.success) {
        message.success(response.message);
        if (response.failed_urls.length > 0) {
          console.warn('Failed URLs:', response.failed_urls);
          message.warning(`部分URL操作失败，请查看控制台了解详情`);
        }
      } else {
        console.error('Batch disable failed:', response);
        message.error(`批量禁用失败: ${response.message || '未知错误'}`);
      }
      setSelectedUrls([]);
      // 刷新URL列表和统计信息
      onRefresh();
      onRefreshStats();
    } catch (error: any) {
      console.error('Batch disable error:', error);
      message.error(`批量禁用失败: ${error.message}`);
    }
  };

  const handleBatchRemove = async () => {
    if (selectedUrls.length === 0) {
      message.warning('请先选择要移除的URL');
      return;
    }

    Modal.confirm({
      title: '确认移除',
      content: `确定要从任务中移除选中的 ${selectedUrls.length} 个URL吗？`,
      onOk: async () => {
        try {
          const response = await removeUrlsBatch(taskId, selectedUrls as string[]);
          if (response.success) {
            message.success(response.message);
            if (response.failed_urls.length > 0) {
              console.warn('Failed URLs:', response.failed_urls);
            }
          } else {
            message.error('批量移除失败');
          }
          setSelectedUrls([]);
          onRefresh();
        } catch (error: any) {
          message.error(`批量移除失败: ${error.message}`);
        }
      },
    });
  };

  // 切换URL状态
  const handleToggleUrlStatus = async (url: TaskUrl) => {
    try {
      const newStatus = url.status === 'active' ? 'disabled' : 'active';
      console.log(`Updating URL ${url.id} status from ${url.status} to ${newStatus} for task ${taskId}`);

      const response = await updateUrlStatus(taskId, url.id, newStatus);
      console.log('Update response:', response);

      if (response.success) {
        message.success(`URL状态已更新为${newStatus === 'active' ? '活跃' : '禁用'}`);
        // 刷新URL列表和统计信息
        onRefresh();
        onRefreshStats();
      } else {
        console.error('Update failed:', response);
        message.error(`状态更新失败: ${response.message || '未知错误'}`);
      }
    } catch (error: any) {
      console.error('Update error:', error);
      message.error(`状态更新失败: ${error.message}`);
    }
  };

  // 移除单个URL
  const handleRemoveUrl = async (url: TaskUrl) => {
    Modal.confirm({
      title: '确认移除',
      content: `确定要从任务中移除这个URL吗？\n${url.url}`,
      onOk: async () => {
        try {
          const response = await removeUrl(taskId, url.id);
          if (response.success) {
            message.success('URL已移除');
            onRefresh();
          } else {
            message.error('移除失败');
          }
        } catch (error: any) {
          message.error(`移除失败: ${error.message}`);
        }
      },
    });
  };

  // 从URL池添加URL
  const handleAddUrlsFromPool = async (selectedUrlIds: string[]) => {
    try {
      const response = await addUrlsToTask(taskId, selectedUrlIds);
      if (response.success) {
        message.success(response.message);
        if (response.failed_urls && response.failed_urls.length > 0) {
          console.warn('Failed URLs:', response.failed_urls);
        }
        setShowUrlPoolModal(false);
        onRefresh();
      } else {
        message.error('添加URL失败');
      }
    } catch (error: any) {
      message.error(`添加URL失败: ${error.message}`);
    }
  };

  const urlColumns: ColumnsType<TaskUrl> = [
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      ellipsis: true,
      render: (url: string) => (
        <Tooltip title={url}>
          <a href={url} target="_blank" rel="noopener noreferrer">
            {url}
          </a>
        </Tooltip>
      ),
    },
    {
      title: '平台',
      dataIndex: 'platform',
      key: 'platform',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string, record: TaskUrl) => (
        <Tag
          color={status === 'active' ? 'green' : 'red'}
          style={{ cursor: 'pointer' }}
          onClick={() => handleToggleUrlStatus(record)}
        >
          {status === 'active' ? '活跃' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '来源文件',
      dataIndex: 'source_file',
      key: 'source_file',
      width: 150,
      ellipsis: true,
    },
    {
      title: '添加时间',
      dataIndex: 'added_at',
      key: 'added_at',
      width: 180,
    },
    {
      title: '最后检查',
      dataIndex: 'last_check',
      key: 'last_check',
      width: 180,
      render: (time: string) => time || '未检查',
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record: TaskUrl) => (
        <Space size="small">
          <Tooltip title={record.status === 'active' ? '禁用' : '启用'}>
            <Button
              type="text"
              size="small"
              icon={record.status === 'active' ? <StopOutlined /> : <CheckCircleOutlined />}
              onClick={() => handleToggleUrlStatus(record)}
            />
          </Tooltip>
          <Tooltip title="从任务中移除">
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleRemoveUrl(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 工具栏 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                icon={<FilterOutlined />}
                onClick={() => setShowFilters(!showFilters)}
              >
                筛选 {showFilters ? '收起' : '展开'}
              </Button>
              {selectedUrls.length > 0 && (
                <>
                  <Button
                    type="primary"
                    size="small"
                    icon={<CheckCircleOutlined />}
                    onClick={handleBatchEnable}
                  >
                    批量启用 ({selectedUrls.length})
                  </Button>
                  <Button
                    size="small"
                    icon={<StopOutlined />}
                    onClick={handleBatchDisable}
                  >
                    批量禁用 ({selectedUrls.length})
                  </Button>
                  <Button
                    danger
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={handleBatchRemove}
                  >
                    批量移除
                  </Button>
                </>
              )}
            </Space>
          </Col>
          <Col>
            <Space>
              <Input.Search
                placeholder="搜索URL..."
                style={{ width: 250 }}
                onSearch={(value) => updateFilter('search', value || undefined)}
                allowClear
              />
              <Button icon={<ReloadOutlined />} onClick={onRefresh}>
                刷新
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setShowUrlPoolModal(true)}
              >
                添加URL
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 筛选器 */}
      {showFilters && (
        <Card size="small" style={{ marginBottom: 16, backgroundColor: '#fafafa' }}>
          <Row gutter={16}>
            <Col span={6}>
              <Text strong>平台:</Text>
              <Select
                style={{ width: '100%', marginTop: 4 }}
                placeholder="选择平台"
                allowClear
                value={filters.platform}
                onChange={(value) => updateFilter('platform', value)}
              >
                <Option value="">全部平台</Option>
                {platforms.map(platform => (
                  <Option key={platform} value={platform}>
                    {platform}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <Text strong>状态:</Text>
              <Select
                style={{ width: '100%', marginTop: 4 }}
                placeholder="选择状态"
                allowClear
                value={filters.status}
                onChange={(value) => updateFilter('status', value)}
              >
                <Option value="">全部状态</Option>
                <Option value="active">活跃</Option>
                <Option value="disabled">禁用</Option>
              </Select>
            </Col>
            <Col span={6}>
              <Text strong>来源文件:</Text>
              <Select
                style={{ width: '100%', marginTop: 4 }}
                placeholder="选择来源文件"
                allowClear
                value={filters.source_file}
                onChange={(value) => updateFilter('source_file', value)}
              >
                <Option value="">全部文件</Option>
                {sourceFiles.map(file => (
                  <Option key={file} value={file}>
                    {file}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <div style={{ marginTop: 24 }}>
                <Button onClick={clearFilters}>
                  清空筛选
                </Button>
              </div>
            </Col>
          </Row>
        </Card>
      )}

      {/* URL列表 */}
      <Card title={`URL列表 (${filteredUrls.length})`}>
        <Table
          columns={urlColumns}
          dataSource={getCurrentPageData()}
          rowKey="id"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 个URL`,
            pageSizeOptions: ['10', '20', '50', '100'],
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
          }}
          rowSelection={{
            selectedRowKeys: selectedUrls,
            onChange: (selectedRowKeys) => {
              console.log('Selected URLs changed:', selectedRowKeys);
              setSelectedUrls(selectedRowKeys);
            },
            preserveSelectedRowKeys: true,
            getCheckboxProps: (record: TaskUrl) => ({
              name: record.id,
            }),
          }}
        />
      </Card>

      {/* URL池选择模态框 */}
      <UrlPoolSelector
        visible={showUrlPoolModal}
        onCancel={() => setShowUrlPoolModal(false)}
        onConfirm={handleAddUrlsFromPool}
        excludeUrls={taskUrls.map(url => url.id)}
      />

    </div>
  );
};

// Worker分配Tab组件
const WorkerAssignmentTab: React.FC<{
  taskId: string;
  taskAssignments: any[];
  taskUrls: ExtendedTaskUrl[];
  onRefresh: () => void;
}> = ({ taskId, taskAssignments, taskUrls, onRefresh }) => {
  const [loading, setLoading] = useState(false);
  const [taskWorkers, setTaskWorkers] = useState<any[]>([]);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingWorker, setEditingWorker] = useState<any>(null);

  // URL处理详情表格分页状态
  const [urlDetailsPagination, setUrlDetailsPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  // 获取任务Worker列表
  const fetchTaskWorkers = async () => {
    try {
      const workers = await workerApi.getTaskWorkers(taskId);
      setTaskWorkers(workers);
    } catch (error: any) {
      console.error('Failed to fetch task workers:', error);
      message.error(`获取Worker列表失败: ${error.message}`);
    }
  };

  // 刷新数据
  const handleRefresh = async () => {
    setLoading(true);
    await Promise.all([
      onRefresh(),
      fetchTaskWorkers()
    ]);
    setLoading(false);
  };

  // 初始化时获取Worker列表
  useEffect(() => {
    fetchTaskWorkers();
  }, [taskId]);

  // 更新URL详情分页总数
  useEffect(() => {
    setUrlDetailsPagination(prev => ({
      ...prev,
      total: taskUrls.length
    }));
  }, [taskUrls]);

  // 处理URL详情表格分页变化
  const handleUrlDetailsTableChange = (page: number, pageSize: number) => {
    setUrlDetailsPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize,
    }));
  };

  // 编辑Worker
  const handleEditWorker = (worker: any) => {
    setEditingWorker(worker);
    setEditModalVisible(true);
  };

  // 移除Worker（只移除指定Worker）
  const handleRemoveWorker = async (worker: any) => {
    try {
      await workerApi.removeWorkerFromTask(taskId, worker.worker_id);
      message.success('Worker移除成功');
      await handleRefresh();
    } catch (error: any) {
      message.error(`移除失败: ${error.message}`);
    }
  };

  // 保存编辑
  const handleSaveEdit = async (values: any) => {
    try {
      // 这里需要实现更新Worker分配的API调用
      message.success('Worker设置更新成功');
      setEditModalVisible(false);
      setEditingWorker(null);
      await handleRefresh();
    } catch (error: any) {
      message.error(`更新失败: ${error.message}`);
    }
  };

  // Worker列表表格列定义
  const workerColumns: ColumnsType<any> = [
    {
      title: 'Worker名称',
      dataIndex: 'worker_name',
      key: 'worker_name',
      width: 150,
      render: (name: string, record: any) => (
        <div>
          <Text strong>{name}</Text>
          <div style={{ fontSize: '12px', color: '#666' }}>
            ID: {record.worker_id.substring(0, 8)}...
          </div>
        </div>
      )
    },
    {
      title: 'Worker状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => {
        const statusConfig = {
          'active': { color: 'green', text: '活跃' },
          'inactive': { color: 'orange', text: '非活跃' },
          'busy': { color: 'blue', text: '忙碌' },
          'error': { color: 'red', text: '错误' },
          'maintenance': { color: 'default', text: '维护' }
        };
        const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      render: (priority: string) => {
        const priorityConfig = {
          'high': { color: 'red', text: '高' },
          'normal': { color: 'blue', text: '普通' },
          'low': { color: 'default', text: '低' }
        };
        const config = priorityConfig[priority as keyof typeof priorityConfig] || { color: 'default', text: priority };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '负载情况',
      key: 'load',
      width: 120,
      render: (_, record) => (
        <div>
          <div>{record.current_tasks}/{record.allocated_concurrent}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            利用率: {(record.utilization_rate * 100).toFixed(1)}%
          </div>
        </div>
      )
    },
    {
      title: '成功率',
      dataIndex: 'success_rate',
      key: 'success_rate',
      width: 100,
      render: (rate: number) => (
        <span style={{ color: rate >= 0.9 ? '#52c41a' : rate >= 0.7 ? '#faad14' : '#ff4d4f' }}>
          {(rate * 100).toFixed(1)}%
        </span>
      )
    },
    {
      title: '健康分数',
      dataIndex: 'health_score',
      key: 'health_score',
      width: 100,
      render: (score: number) => (
        <span style={{ color: score >= 80 ? '#52c41a' : score >= 60 ? '#faad14' : '#ff4d4f' }}>
          {score.toFixed(0)}
        </span>
      )
    },
    {
      title: '配置信息',
      key: 'config_info',
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            前端: {record.crawler_config_name || record.crawler_config_id || '未知'}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            后端: {record.backend_config_name || record.backend_config_id || '未知'}
          </div>
        </div>
      )
    },
    {
      title: '分配信息',
      key: 'assignment_info',
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            分配: {record.assignment_name || '未知'}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            状态: {record.assignment_status || '未知'}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            分配时间: {record.assigned_at ? new Date(record.assigned_at).toLocaleString() : '-'}
          </div>
        </div>
      )
    },
    {
      title: 'URL处理',
      key: 'url_processing',
      width: 120,
      render: (_, record) => (
        <div>
          <div>已分配: {record.assigned_urls || 0}</div>
          <div>已处理: {record.processed_urls || 0}</div>
          <div>失败: {record.failed_urls || 0}</div>
        </div>
      )
    },
    {
      title: '最后活动',
      dataIndex: 'last_task_time',
      key: 'last_task_time',
      width: 150,
      render: (time: string) => time ? new Date(time).toLocaleString() : '从未'
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            size="small"
            type="link"
            onClick={() => handleEditWorker(record)}
          >
            设置
          </Button>
          <Button
            size="small"
            type="link"
            danger
            onClick={() => handleRemoveWorker(record)}
          >
            移除
          </Button>
        </Space>
      )
    }
  ];

  // URL处理详情表格列定义
  const urlColumns: ColumnsType<ExtendedTaskUrl> = [
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      width: 300,
      render: (url: string) => (
        <Tooltip title={url}>
          <span style={{
            display: 'block',
            maxWidth: '280px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {url}
          </span>
        </Tooltip>
      )
    },
    {
      title: '分配Worker',
      dataIndex: 'assigned_worker_id',
      key: 'assigned_worker_id',
      width: 180,
      render: (workerId: string) => workerId ? (
        <Tag color="blue">{workerId}</Tag>
      ) : (
        <Tag color="default">未分配</Tag>
      )
    },
    {
      title: 'URL状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '活跃' : '禁用'}
        </Tag>
      )
    },
    {
      title: '处理状态',
      dataIndex: 'processing_status',
      key: 'processing_status',
      width: 120,
      render: (status: string) => {
        const statusConfig = {
          'pending': { color: 'orange', text: '待处理' },
          'processing': { color: 'blue', text: '处理中' },
          'completed': { color: 'green', text: '已完成' },
          'failed': { color: 'red', text: '失败' },
          'skipped': { color: 'default', text: '跳过' }
        };
        const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status || '未知' };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '最后处理时间',
      dataIndex: 'last_processed_at',
      key: 'last_processed_at',
      width: 180,
      render: (time: string) => time ? new Date(time).toLocaleString() : '-'
    }
  ];

  // 统计信息
  const assignedUrls = taskUrls.filter(url => url.assigned_worker_id);
  const unassignedUrls = taskUrls.filter(url => !url.assigned_worker_id);
  const activeWorkers = taskWorkers.filter(worker =>
    worker.status === 'active' || worker.status === 'busy'
  ).length;
  const totalWorkers = taskWorkers.length;

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                {totalWorkers}
              </div>
              <div style={{ color: '#666' }}>指派Worker数</div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                {activeWorkers}
              </div>
              <div style={{ color: '#666' }}>活跃Worker</div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#722ed1' }}>
                {assignedUrls.length}
              </div>
              <div style={{ color: '#666' }}>已分配URL</div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>
                {unassignedUrls.length}
              </div>
              <div style={{ color: '#666' }}>未分配URL</div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Worker列表 */}
      <Card
        title="指派的Worker"
        size="small"
        extra={
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
            size="small"
          >
            刷新
          </Button>
        }
        style={{ marginBottom: 24 }}
      >
        <Table
          columns={workerColumns}
          dataSource={taskWorkers}
          rowKey="worker_id"
          size="small"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个Worker`
          }}
          locale={{
            emptyText: (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无指派的Worker"
              />
            )
          }}
        />
      </Card>

      {/* URL处理详情 */}
      <Card title="URL处理详情" size="small">
        <Table
          columns={urlColumns}
          dataSource={taskUrls}
          rowKey="id"
          size="small"
          pagination={{
            current: urlDetailsPagination.current,
            pageSize: urlDetailsPagination.pageSize,
            total: urlDetailsPagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个URL`,
            pageSizeOptions: ['10', '20', '50', '100'],
            onChange: handleUrlDetailsTableChange,
            onShowSizeChange: handleUrlDetailsTableChange,
          }}
          locale={{
            emptyText: (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无URL数据"
              />
            )
          }}
        />
      </Card>

      {/* 编辑Worker模态框 */}
      <Modal
        title="Worker设置"
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          setEditingWorker(null);
        }}
        footer={[
          <Button key="cancel" onClick={() => setEditModalVisible(false)}>
            取消
          </Button>,
          <Button key="save" type="primary" onClick={() => handleSaveEdit({})}>
            保存
          </Button>
        ]}
      >
        {editingWorker && (
          <div>
            <p><strong>Worker名称:</strong> {editingWorker.worker_name}</p>
            <p><strong>Worker ID:</strong> {editingWorker.worker_id}</p>
            <p><strong>当前状态:</strong> {editingWorker.status}</p>
            <p><strong>分配状态:</strong> {editingWorker.assignment_status}</p>
            <p>Worker设置功能开发中...</p>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default TaskDetail;
