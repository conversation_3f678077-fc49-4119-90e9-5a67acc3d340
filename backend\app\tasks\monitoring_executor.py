"""
监控任务执行器
实现定时监控任务的核心执行逻辑
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from celery import Task

from app.celery_app import celery_app
from app.core.task_manager import get_task_manager
from app.core.task_splitter import TaskPriority
from app.services.task_storage_service import TaskStorageService
from app.core.redis_client import get_redis_client
from app.utils.datetime_utils import get_local_isoformat

logger = logging.getLogger(__name__)


class MonitoringTaskExecutor:
    """监控任务执行器"""
    
    def __init__(self, task_id: str):
        self.task_id = task_id
        self.task_storage = None
        self.redis_client = None
        self.task_manager = None
        
    async def initialize(self):
        """初始化执行器"""
        try:
            # 初始化服务
            redis_url = "redis://redis:6379/0"  # Docker环境
            self.task_storage = TaskStorageService(redis_url)
            self.redis_client = await get_redis_client()
            self.task_manager = await get_task_manager()
            
            logger.info(f"MonitoringTaskExecutor initialized for task {self.task_id}")
            
        except Exception as e:
            logger.error(f"Failed to initialize MonitoringTaskExecutor: {e}")
            raise
    
    async def execute(self) -> Dict[str, Any]:
        """执行监控任务"""
        execution_start = datetime.now()
        execution_id = f"exec_{self.task_id}_{int(execution_start.timestamp())}"
        
        try:
            # 初始化执行器
            await self.initialize()
            
            # 获取任务数据
            task_data = await self.task_storage.get_task(self.task_id)
            if not task_data:
                raise ValueError(f"Task {self.task_id} not found")
            
            # 检查任务状态
            if task_data.get("status") != "active":
                logger.info(f"Task {self.task_id} is not active, skipping execution")
                return {
                    "success": False,
                    "message": "Task is not active",
                    "task_id": self.task_id,
                    "execution_id": execution_id
                }
            
            # 更新任务执行状态
            await self._update_task_execution_start(execution_id)
            
            # 获取任务URL列表
            urls = await self._get_task_urls()
            if not urls:
                logger.warning(f"No URLs found for task {self.task_id}")
                return {
                    "success": False,
                    "message": "No URLs to process",
                    "task_id": self.task_id,
                    "execution_id": execution_id
                }
            
            # 执行URL批量处理
            result = await self._execute_url_batch(urls, execution_id)
            
            # 更新任务执行完成状态
            await self._update_task_execution_complete(execution_id, result)
            
            # 计算下次执行时间
            await self._update_next_run_time()
            
            logger.info(f"Task {self.task_id} executed successfully: {result}")
            
            return {
                "success": True,
                "message": "Task executed successfully",
                "task_id": self.task_id,
                "execution_id": execution_id,
                "urls_processed": len(urls),
                "execution_time": (datetime.now() - execution_start).total_seconds(),
                "result": result
            }
            
        except Exception as e:
            logger.error(f"Failed to execute monitoring task {self.task_id}: {e}")
            
            # 更新任务执行失败状态
            await self._update_task_execution_failed(execution_id, str(e))
            
            return {
                "success": False,
                "message": str(e),
                "task_id": self.task_id,
                "execution_id": execution_id,
                "execution_time": (datetime.now() - execution_start).total_seconds()
            }
        
        finally:
            # 清理资源
            await self._cleanup()
    
    async def _get_task_urls(self) -> List[str]:
        """获取任务的URL列表"""
        try:
            # 从Redis获取任务关联的URL
            url_ids = await self.task_storage.get_task_urls(self.task_id)
            if not url_ids:
                return []

            # 使用UrlPoolService获取URL详细信息
            from ..services.url_pool_service import UrlPoolService
            import redis.asyncio as redis

            redis_client = redis.from_url("redis://redis:6379/0")
            url_pool_service = UrlPoolService(redis_client)

            urls = []
            for url_id in url_ids:
                url_item = await url_pool_service.get_url_by_id(url_id)
                if url_item and url_item.status == "active":
                    urls.append(url_item.url)

            await redis_client.close()
            logger.info(f"Retrieved {len(urls)} active URLs for task {self.task_id}")
            return urls

        except Exception as e:
            logger.error(f"Failed to get URLs for task {self.task_id}: {e}")
            return []
    
    async def _execute_url_batch(self, urls: List[str], execution_id: str) -> Dict[str, Any]:
        """执行URL批量处理"""
        try:
            # 获取任务配置
            task_data = await self.task_storage.get_task(self.task_id)
            platform = task_data.get("platform", "mercadolibre")
            
            # 提交任务到TaskManager
            submission_id = await self.task_manager.submit_task(
                task_id=int(self.task_id) if self.task_id.isdigit() else hash(self.task_id),
                urls=urls,
                platform=platform,
                priority=TaskPriority.NORMAL,  # 定时任务使用普通优先级
                options={
                    "execution_id": execution_id,
                    "scheduled_execution": True,
                    "task_type": "monitoring"
                }
            )
            
            logger.info(f"Submitted batch processing for task {self.task_id}, submission_id: {submission_id}")
            
            return {
                "submission_id": submission_id,
                "urls_count": len(urls),
                "platform": platform,
                "status": "submitted"
            }
            
        except Exception as e:
            logger.error(f"Failed to execute URL batch for task {self.task_id}: {e}")
            raise
    
    async def _update_task_execution_start(self, execution_id: str):
        """更新任务执行开始状态"""
        try:
            now = get_local_isoformat()
            
            # 更新任务状态
            updates = {
                "is_running": True,
                "last_run": now,
                "updated_at": now
            }
            
            await self.task_storage.update_task(self.task_id, updates)
            
            # 记录执行开始
            execution_record = {
                "execution_id": execution_id,
                "task_id": self.task_id,
                "status": "running",
                "started_at": now,
                "execution_type": "scheduled"
            }
            
            await self.redis_client.setex(
                f"task_execution:{execution_id}",
                3600,  # 1小时过期
                str(execution_record)
            )
            
            logger.info(f"Task execution started: {self.task_id}, execution_id: {execution_id}")
            
        except Exception as e:
            logger.error(f"Failed to update task execution start: {e}")
    
    async def _update_task_execution_complete(self, execution_id: str, result: Dict[str, Any]):
        """更新任务执行完成状态"""
        try:
            now = get_local_isoformat()
            
            # 更新任务状态
            updates = {
                "is_running": False,
                "last_run": now,
                "updated_at": now
            }
            
            await self.task_storage.update_task(self.task_id, updates)
            
            # 更新执行记录
            execution_record = {
                "execution_id": execution_id,
                "task_id": self.task_id,
                "status": "completed",
                "started_at": result.get("started_at"),
                "completed_at": now,
                "execution_type": "scheduled",
                "result": result
            }
            
            await self.redis_client.setex(
                f"task_execution:{execution_id}",
                86400,  # 24小时保留
                str(execution_record)
            )
            
            logger.info(f"Task execution completed: {self.task_id}, execution_id: {execution_id}")
            
        except Exception as e:
            logger.error(f"Failed to update task execution complete: {e}")
    
    async def _update_task_execution_failed(self, execution_id: str, error_message: str):
        """更新任务执行失败状态"""
        try:
            now = get_local_isoformat()
            
            # 更新任务状态
            updates = {
                "is_running": False,
                "updated_at": now
            }
            
            await self.task_storage.update_task(self.task_id, updates)
            
            # 记录执行失败
            execution_record = {
                "execution_id": execution_id,
                "task_id": self.task_id,
                "status": "failed",
                "completed_at": now,
                "execution_type": "scheduled",
                "error": error_message
            }
            
            await self.redis_client.setex(
                f"task_execution:{execution_id}",
                86400,  # 24小时保留
                str(execution_record)
            )
            
            logger.error(f"Task execution failed: {self.task_id}, execution_id: {execution_id}, error: {error_message}")
            
        except Exception as e:
            logger.error(f"Failed to update task execution failed: {e}")
    
    async def _update_next_run_time(self):
        """更新下次执行时间"""
        try:
            # 获取任务调度配置
            task_data = await self.task_storage.get_task(self.task_id)
            schedule_config = task_data.get("schedule")
            
            if schedule_config:
                # 计算下次执行时间
                from app.models.monitoring_task import calculate_next_run, ScheduleConfig
                schedule = ScheduleConfig(**schedule_config)
                next_run = calculate_next_run(schedule)
                
                if next_run:
                    updates = {
                        "next_run": next_run.isoformat(),
                        "updated_at": get_local_isoformat()
                    }
                    
                    await self.task_storage.update_task(self.task_id, updates)
                    logger.info(f"Updated next run time for task {self.task_id}: {next_run}")
            
        except Exception as e:
            logger.error(f"Failed to update next run time for task {self.task_id}: {e}")
    
    async def _cleanup(self):
        """清理资源"""
        try:
            if self.redis_client:
                await self.redis_client.close()
            
        except Exception as e:
            logger.error(f"Failed to cleanup resources: {e}")


# Celery任务定义
class MonitoringTask(Task):
    """监控任务基类"""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """任务失败时的回调"""
        logger.error(f"Monitoring task failed: {task_id}, exception: {exc}")
    
    def on_success(self, retval, task_id, args, kwargs):
        """任务成功时的回调"""
        logger.info(f"Monitoring task succeeded: {task_id}, result: {retval}")


@celery_app.task(
    bind=True,
    base=MonitoringTask,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 300}  # 5分钟后重试
)
def execute_monitoring_task(self, task_id: str) -> Dict[str, Any]:
    """执行监控任务 - Celery任务入口"""
    logger.info(f"Starting monitoring task execution: {task_id}")
    
    # 创建执行器并运行
    executor = MonitoringTaskExecutor(task_id)
    return asyncio.run(executor.execute())
