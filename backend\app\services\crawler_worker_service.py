"""
新架构：爬虫Worker管理服务
管理配置组合和Worker兼容性检查
"""

import json
import uuid
import logging
import redis
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Optional, Dict, Any, Set
from fastapi import HTTPException

from ..schemas.crawler_worker import (
    CrawlerWorker, CrawlerWorkerCreate, CrawlerWorkerUpdate,
    CrawlerWorkerSummary, CrawlerWorkerDetail, CrawlerWorkerStats,
    WorkerDetailStats, TaskExecutionRecord, WorkerCompatibilityCheck, WorkerGroupCompatibilityCheck,
    WorkerStatus, WorkerPriority
)
from ..services.crawler_config_service import crawler_config_service
from ..services.backend_config_service import backend_config_service

logger = logging.getLogger(__name__)


class CrawlerWorkerService:
    """爬虫Worker管理服务"""
    
    def __init__(self):
        # 使用现有的Redis连接模式
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.data_dir = Path("data/crawler_workers")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Redis键模式
        self.keys = {
            'worker': 'crawler_worker:{}',
            'worker_list': 'crawler_worker:list',
            'worker_by_name': 'crawler_worker:name:{}',
            'workers_by_backend': 'crawler_worker:backend:{}',
            'workers_by_config': 'crawler_worker:config:{}',
            'worker_stats': 'crawler_worker:stats'
        }
    
    async def create_worker(self, worker_data: CrawlerWorkerCreate) -> CrawlerWorker:
        """创建Worker"""
        try:
            # 检查名称是否已存在
            existing_worker = await self.get_worker_by_name(worker_data.worker_name)
            if existing_worker:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Worker名称 '{worker_data.worker_name}' 已存在"
                )
            
            # 验证配置是否存在
            crawler_config = await crawler_config_service.get_config(worker_data.crawler_config_id)
            if not crawler_config:
                raise HTTPException(
                    status_code=400, 
                    detail=f"爬取配置 {worker_data.crawler_config_id} 不存在"
                )
            
            backend_config = await backend_config_service.get_config(worker_data.backend_config_id)
            if not backend_config:
                raise HTTPException(
                    status_code=400, 
                    detail=f"后端配置 {worker_data.backend_config_id} 不存在"
                )
            
            # 检查配置状态
            if not crawler_config.is_active:
                raise HTTPException(
                    status_code=400, 
                    detail=f"爬取配置 '{crawler_config.config_name}' 不是活跃状态"
                )
            
            if not backend_config.is_available:
                raise HTTPException(
                    status_code=400, 
                    detail=f"后端配置 '{backend_config.backend_name}' 不可用"
                )
            
            # 检查最大并发任务数是否超过后端限制
            if worker_data.max_concurrent_tasks > backend_config.max_concurrent:
                raise HTTPException(
                    status_code=400,
                    detail=f"最大并发任务数 ({worker_data.max_concurrent_tasks}) 超过后端最大并发数 ({backend_config.max_concurrent})"
                )

            # 检查分配的并发数是否超过最大并发任务数
            if worker_data.allocated_concurrent > worker_data.max_concurrent_tasks:
                raise HTTPException(
                    status_code=400,
                    detail=f"分配的并发数 ({worker_data.allocated_concurrent}) 不能超过最大并发任务数 ({worker_data.max_concurrent_tasks})"
                )
            
            # 生成Worker ID
            worker_id = str(uuid.uuid4())
            now = datetime.now()
            
            # 创建Worker对象
            worker = CrawlerWorker(
                worker_id=worker_id,
                worker_name=worker_data.worker_name,
                description=worker_data.description,
                crawler_config_id=worker_data.crawler_config_id,
                backend_config_id=worker_data.backend_config_id,
                priority=worker_data.priority,
                max_concurrent_tasks=worker_data.max_concurrent_tasks,
                allocated_concurrent=worker_data.allocated_concurrent,
                max_tasks_per_hour=worker_data.max_tasks_per_hour,
                created_at=now,
                updated_at=now,
                created_by=worker_data.created_by
            )
            
            # 保存Worker
            await self._save_worker(worker)
            
            # 添加到各种索引
            self.redis_client.sadd(self.keys['worker_list'], worker_id)
            self.redis_client.set(
                self.keys['worker_by_name'].format(worker_data.worker_name), 
                worker_id
            )
            self.redis_client.sadd(
                self.keys['workers_by_backend'].format(worker_data.backend_config_id), 
                worker_id
            )
            self.redis_client.sadd(
                self.keys['workers_by_config'].format(worker_data.crawler_config_id), 
                worker_id
            )
            
            logger.info(f"Created crawler worker: {worker_id} ({worker_data.worker_name})")
            return worker
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to create crawler worker: {e}")
            raise HTTPException(status_code=500, detail=f"创建Worker失败: {str(e)}")
    
    async def get_worker(self, worker_id: str) -> Optional[CrawlerWorker]:
        """获取Worker"""
        try:
            # 从Redis获取
            worker_data = self.redis_client.get(self.keys['worker'].format(worker_id))
            if worker_data:
                worker_dict = json.loads(worker_data)
                return CrawlerWorker(**worker_dict)
            
            # 从文件获取
            worker_file = self.data_dir / f"{worker_id}.json"
            if worker_file.exists():
                with open(worker_file, 'r', encoding='utf-8') as f:
                    worker_dict = json.load(f)
                    worker = CrawlerWorker(**worker_dict)
                    # 同步到Redis
                    await self._save_worker(worker)
                    return worker
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get crawler worker {worker_id}: {e}")
            return None
    
    async def get_worker_by_name(self, worker_name: str) -> Optional[CrawlerWorker]:
        """根据名称获取Worker"""
        try:
            worker_id = self.redis_client.get(self.keys['worker_by_name'].format(worker_name))
            if worker_id:
                worker_id_str = worker_id if isinstance(worker_id, str) else worker_id.decode()
                return await self.get_worker(worker_id_str)
            return None
        except Exception as e:
            logger.error(f"Failed to get worker by name {worker_name}: {e}")
            return None
    
    async def get_worker_detail(self, worker_id: str) -> Optional[CrawlerWorkerDetail]:
        """获取Worker详细信息（包含关联配置）"""
        try:
            worker = await self.get_worker(worker_id)
            if not worker:
                return None
            
            # 获取关联的配置
            crawler_config = await crawler_config_service.get_config(worker.crawler_config_id)
            backend_config = await backend_config_service.get_config(worker.backend_config_id)
            
            # 执行兼容性检查
            compatibility_check = await self.check_worker_compatibility(worker_id)
            
            return CrawlerWorkerDetail(
                worker=worker,
                crawler_config=crawler_config,
                backend_config=backend_config,
                compatibility_check=compatibility_check.dict() if compatibility_check else None
            )
            
        except Exception as e:
            logger.error(f"Failed to get worker detail {worker_id}: {e}")
            return None
    
    async def update_worker(self, worker_id: str, update_data: CrawlerWorkerUpdate) -> Optional[CrawlerWorker]:
        """更新Worker"""
        try:
            # 获取现有Worker
            worker = await self.get_worker(worker_id)
            if not worker:
                raise HTTPException(status_code=404, detail="Worker不存在")
            
            # 检查名称冲突
            if update_data.worker_name and update_data.worker_name != worker.worker_name:
                existing_worker = await self.get_worker_by_name(update_data.worker_name)
                if existing_worker and existing_worker.worker_id != worker_id:
                    raise HTTPException(
                        status_code=400, 
                        detail=f"Worker名称 '{update_data.worker_name}' 已存在"
                    )
            
            # 验证配置更新
            if update_data.crawler_config_id and update_data.crawler_config_id != worker.crawler_config_id:
                crawler_config = await crawler_config_service.get_config(update_data.crawler_config_id)
                if not crawler_config or not crawler_config.is_active:
                    raise HTTPException(
                        status_code=400, 
                        detail="指定的爬取配置不存在或不活跃"
                    )
            
            if update_data.backend_config_id and update_data.backend_config_id != worker.backend_config_id:
                backend_config = await backend_config_service.get_config(update_data.backend_config_id)
                if not backend_config or not backend_config.is_available:
                    raise HTTPException(
                        status_code=400,
                        detail="指定的后端配置不存在或不可用"
                    )

            # 验证并发配置
            max_concurrent = update_data.max_concurrent_tasks or worker.max_concurrent_tasks
            allocated_concurrent = update_data.allocated_concurrent or worker.allocated_concurrent

            # 获取后端配置限制
            backend_config_id = update_data.backend_config_id or worker.backend_config_id
            backend_config = await backend_config_service.get_config(backend_config_id)
            if backend_config:
                if max_concurrent > backend_config.max_concurrent:
                    raise HTTPException(
                        status_code=400,
                        detail=f"最大并发任务数 ({max_concurrent}) 超过后端最大并发数 ({backend_config.max_concurrent})"
                    )

            # 检查分配的并发数是否超过最大并发任务数
            if allocated_concurrent > max_concurrent:
                raise HTTPException(
                    status_code=400,
                    detail=f"分配的并发数 ({allocated_concurrent}) 不能超过最大并发任务数 ({max_concurrent})"
                )
            
            # 更新Worker
            update_dict = update_data.dict(exclude_unset=True)
            old_name = worker.worker_name
            old_backend_id = worker.backend_config_id
            old_config_id = worker.crawler_config_id
            
            for field, value in update_dict.items():
                if hasattr(worker, field):
                    setattr(worker, field, value)
            
            worker.updated_at = datetime.now()
            
            # 保存更新后的Worker
            await self._save_worker(worker)
            
            # 更新索引
            if update_data.worker_name and update_data.worker_name != old_name:
                self.redis_client.delete(self.keys['worker_by_name'].format(old_name))
                self.redis_client.set(
                    self.keys['worker_by_name'].format(update_data.worker_name), 
                    worker_id
                )
            
            if update_data.backend_config_id and update_data.backend_config_id != old_backend_id:
                self.redis_client.srem(self.keys['workers_by_backend'].format(old_backend_id), worker_id)
                self.redis_client.sadd(self.keys['workers_by_backend'].format(update_data.backend_config_id), worker_id)
            
            if update_data.crawler_config_id and update_data.crawler_config_id != old_config_id:
                self.redis_client.srem(self.keys['workers_by_config'].format(old_config_id), worker_id)
                self.redis_client.sadd(self.keys['workers_by_config'].format(update_data.crawler_config_id), worker_id)
            
            logger.info(f"Updated crawler worker: {worker_id}")
            return worker
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to update crawler worker {worker_id}: {e}")
            raise HTTPException(status_code=500, detail=f"更新Worker失败: {str(e)}")
    
    async def delete_worker(self, worker_id: str) -> bool:
        """删除Worker"""
        try:
            # 获取Worker信息
            worker = await self.get_worker(worker_id)
            if not worker:
                raise HTTPException(status_code=404, detail="Worker不存在")
            
            # 检查是否正在使用
            if worker.current_tasks > 0:
                raise HTTPException(
                    status_code=400, 
                    detail="Worker正在执行任务，无法删除"
                )
            
            # 从Redis删除
            self.redis_client.delete(self.keys['worker'].format(worker_id))
            self.redis_client.srem(self.keys['worker_list'], worker_id)
            self.redis_client.delete(self.keys['worker_by_name'].format(worker.worker_name))
            self.redis_client.srem(self.keys['workers_by_backend'].format(worker.backend_config_id), worker_id)
            self.redis_client.srem(self.keys['workers_by_config'].format(worker.crawler_config_id), worker_id)
            
            # 删除文件
            worker_file = self.data_dir / f"{worker_id}.json"
            if worker_file.exists():
                worker_file.unlink()
            
            logger.info(f"Deleted crawler worker: {worker_id}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to delete crawler worker {worker_id}: {e}")
            return False
    
    async def list_workers(
        self, 
        status: Optional[WorkerStatus] = None,
        priority: Optional[WorkerPriority] = None,
        backend_config_id: Optional[str] = None,
        crawler_config_id: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[CrawlerWorkerSummary]:
        """获取Worker列表"""
        try:
            # 获取Worker ID列表
            if backend_config_id:
                worker_ids = self.redis_client.smembers(self.keys['workers_by_backend'].format(backend_config_id))
            elif crawler_config_id:
                worker_ids = self.redis_client.smembers(self.keys['workers_by_config'].format(crawler_config_id))
            else:
                worker_ids = self.redis_client.smembers(self.keys['worker_list'])
            
            if not worker_ids:
                return []
            
            # 获取Worker详情
            workers = []
            for worker_id in worker_ids:
                worker_id_str = worker_id if isinstance(worker_id, str) else worker_id.decode()
                worker = await self.get_worker(worker_id_str)
                if worker:
                    workers.append(worker)
            
            # 过滤
            if status:
                workers = [w for w in workers if w.status == status]
            
            if priority:
                workers = [w for w in workers if w.priority == priority]
            
            # 排序（按优先级和健康评分）
            workers.sort(key=lambda x: (x.priority.value, x.health_score), reverse=True)
            
            # 分页
            workers = workers[offset:offset + limit]
            
            # 转换为摘要（需要获取配置名称）
            summaries = []
            for worker in workers:
                crawler_config = await crawler_config_service.get_config(worker.crawler_config_id)
                backend_config = await backend_config_service.get_config(worker.backend_config_id)
                
                summary = CrawlerWorkerSummary(
                    worker_id=worker.worker_id,
                    worker_name=worker.worker_name,
                    description=worker.description,
                    status=worker.status,
                    priority=worker.priority,
                    crawler_config_name=crawler_config.config_name if crawler_config else "未知配置",
                    backend_config_name=backend_config.backend_name if backend_config else "未知后端",
                    backend_endpoint=backend_config.api_endpoint if backend_config else "未知端点",
                    allocated_concurrent=worker.allocated_concurrent,
                    current_tasks=worker.current_tasks,
                    available_capacity=worker.available_capacity,
                    utilization_rate=worker.utilization_rate,
                    success_rate=worker.success_rate,
                    health_score=worker.health_score,
                    created_at=worker.created_at,
                    last_used=worker.last_used
                )
                summaries.append(summary)
            
            return summaries
            
        except Exception as e:
            logger.error(f"Failed to list crawler workers: {e}")
            return []
    
    async def check_worker_compatibility(self, worker_id: str) -> Optional[WorkerCompatibilityCheck]:
        """检查Worker兼容性"""
        try:
            worker = await self.get_worker(worker_id)
            if not worker:
                return None
            
            result = WorkerCompatibilityCheck(
                worker_id=worker_id,
                is_compatible=True,
                config_exists=False,
                backend_available=False,
                resource_conflict=False,
                errors=[],
                warnings=[],
                suggestions=[],
                conflicted_workers=[]
            )
            
            # 检查配置是否存在
            crawler_config = await crawler_config_service.get_config(worker.crawler_config_id)
            backend_config = await backend_config_service.get_config(worker.backend_config_id)
            
            result.config_exists = crawler_config is not None and backend_config is not None
            
            if not result.config_exists:
                result.is_compatible = False
                if not crawler_config:
                    result.errors.append(f"爬取配置 {worker.crawler_config_id} 不存在")
                if not backend_config:
                    result.errors.append(f"后端配置 {worker.backend_config_id} 不存在")
            
            # 检查后端是否可用
            if backend_config:
                result.backend_available = backend_config.is_available
                if not result.backend_available:
                    result.is_compatible = False
                    result.errors.append(f"后端 '{backend_config.backend_name}' 不可用")
            
            # 检查资源冲突
            conflicted_workers = await self.get_conflicted_workers(worker_id)
            result.resource_conflict = len(conflicted_workers) > 0
            result.conflicted_workers = conflicted_workers
            
            if result.resource_conflict:
                result.warnings.append(f"与 {len(conflicted_workers)} 个Worker存在资源冲突")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to check worker compatibility {worker_id}: {e}")
            return None
    
    async def get_conflicted_workers(self, worker_id: str) -> List[str]:
        """获取与指定Worker冲突的Worker列表"""
        try:
            worker = await self.get_worker(worker_id)
            if not worker:
                return []
            
            # 获取使用相同后端的所有Worker
            backend_workers = self.redis_client.smembers(
                self.keys['workers_by_backend'].format(worker.backend_config_id)
            )
            
            conflicted_workers = []
            for other_worker_id in backend_workers:
                other_worker_id_str = other_worker_id if isinstance(other_worker_id, str) else other_worker_id.decode()
                if other_worker_id_str != worker_id:
                    other_worker = await self.get_worker(other_worker_id_str)
                    if other_worker and other_worker.status == WorkerStatus.ACTIVE:
                        conflicted_workers.append(other_worker_id_str)
            
            return conflicted_workers
            
        except Exception as e:
            logger.error(f"Failed to get conflicted workers for {worker_id}: {e}")
            return []
    
    async def _save_worker(self, worker: CrawlerWorker):
        """保存Worker到Redis和文件"""
        try:
            worker_dict = worker.dict()
            worker_json = json.dumps(worker_dict, default=str, ensure_ascii=False)
            
            # 保存到Redis
            self.redis_client.set(
                self.keys['worker'].format(worker.worker_id), 
                worker_json
            )
            
            # 保存到文件
            worker_file = self.data_dir / f"{worker.worker_id}.json"
            with open(worker_file, 'w', encoding='utf-8') as f:
                json.dump(worker_dict, f, indent=2, ensure_ascii=False, default=str)
            
        except Exception as e:
            logger.error(f"Failed to save worker {worker.worker_id}: {e}")
            raise

    async def get_worker_stats(self, worker_id: str) -> Optional[WorkerDetailStats]:
        """获取Worker统计信息"""
        try:
            worker = await self.get_worker(worker_id)
            if not worker:
                return None

            # 构建统计信息
            stats = WorkerDetailStats(
                worker_id=worker_id,
                total_tasks=worker.total_tasks,
                completed_tasks=worker.completed_tasks,
                failed_tasks=worker.failed_tasks,
                success_rate=worker.success_rate,
                avg_execution_time=worker.avg_task_duration,  # 使用正确的字段名
                current_load=getattr(worker, 'current_load', worker.current_tasks / max(worker.allocated_concurrent, 1)),
                peak_load=getattr(worker, 'peak_load', 0.0),
                uptime_hours=getattr(worker, 'uptime_hours', 0.0),
                last_task_time=worker.last_task_time,
                error_rate=getattr(worker, 'error_rate', worker.error_count / max(worker.total_tasks, 1) if worker.total_tasks > 0 else 0.0),
                throughput_per_hour=getattr(worker, 'throughput_per_hour', 0.0),
                memory_usage_mb=getattr(worker, 'memory_usage_mb', 0.0),
                cpu_usage_percent=getattr(worker, 'cpu_usage_percent', 0.0)
            )

            return stats

        except Exception as e:
            logger.error(f"Failed to get worker stats {worker_id}: {e}")
            return None

    async def get_worker_assignments(self, worker_id: str, limit: int = 10, offset: int = 0) -> List[TaskExecutionRecord]:
        """获取Worker任务分配记录"""
        try:
            all_records = []

            # 1. 从任务执行服务获取执行记录
            worker_executions_key = f"task_execution:worker:{worker_id}"
            execution_ids = self.redis_client.lrange(worker_executions_key, 0, -1)

            for execution_id in execution_ids:
                try:
                    execution_key = f"task_execution:{execution_id}"
                    execution_data = self.redis_client.get(execution_key)
                    if execution_data:
                        execution_dict = json.loads(execution_data)
                        record = TaskExecutionRecord(**execution_dict)
                        all_records.append(record)
                except Exception as e:
                    logger.warning(f"Failed to parse execution {execution_id}: {e}")

            # 2. 从任务分配系统获取分配记录
            assignment_keys = self.redis_client.keys(f"task_assignment:worker:{worker_id}:*")

            for key in assignment_keys:
                try:
                    assignment_data = self.redis_client.get(key)
                    if assignment_data:
                        assignment = json.loads(assignment_data)
                        record = await self._create_execution_record_from_assignment(assignment, worker_id)
                        if record:
                            all_records.append(record)
                except Exception as e:
                    logger.warning(f"Failed to parse assignment {key}: {e}")

            # 3. 从监控任务系统获取相关任务（如果没有执行记录）
            if len(all_records) < limit:
                monitoring_task_keys = self.redis_client.keys("monitoring_task:*")

                for key in monitoring_task_keys:
                    try:
                        task_data = self.redis_client.get(key)
                        if task_data:
                            task = json.loads(task_data)
                            # 检查是否已有执行记录
                            task_id = task.get('id')
                            has_record = any(r.task_id == task_id for r in all_records)

                            if not has_record and await self._worker_executed_task(worker_id, task_id):
                                record = await self._create_execution_record_from_monitoring_task(task, worker_id)
                                if record:
                                    all_records.append(record)
                    except Exception as e:
                        logger.warning(f"Failed to parse monitoring task {key}: {e}")

            # 去重（基于task_id和worker_id）
            seen = set()
            unique_records = []
            for record in all_records:
                key = (record.task_id, record.worker_id)
                if key not in seen:
                    seen.add(key)
                    unique_records.append(record)

            # 按时间排序（最新的在前）
            unique_records.sort(key=lambda x: x.assigned_at, reverse=True)

            # 应用分页
            start_idx = offset
            end_idx = offset + limit
            paginated_records = unique_records[start_idx:end_idx]

            return paginated_records

        except Exception as e:
            logger.error(f"Failed to get worker assignments {worker_id}: {e}")
            return []

    async def _create_execution_record_from_assignment(self, assignment: Dict[str, Any], worker_id: str) -> Optional[TaskExecutionRecord]:
        """从任务分配创建执行记录"""
        try:
            # 获取任务详情
            task_id = assignment.get('task_id')
            task_name = assignment.get('task_name', f"任务 {task_id}")

            # 获取执行状态和时间
            status = assignment.get('status', 'pending')
            assigned_at = datetime.fromisoformat(assignment.get('assigned_at', datetime.now().isoformat()))
            started_at = assignment.get('started_at')
            completed_at = assignment.get('completed_at')

            if started_at:
                started_at = datetime.fromisoformat(started_at)
            if completed_at:
                completed_at = datetime.fromisoformat(completed_at)

            # 计算执行时间
            execution_time = None
            if started_at and completed_at:
                execution_time = (completed_at - started_at).total_seconds()

            # 获取URL统计
            total_urls = assignment.get('total_urls', 0)
            processed_urls = assignment.get('processed_urls', 0)
            success_urls = assignment.get('success_urls', 0)
            failed_urls = assignment.get('failed_urls', 0)

            record = TaskExecutionRecord(
                execution_id=assignment.get('assignment_id', f"exec_{uuid.uuid4().hex[:8]}"),
                assignment_id=assignment.get('assignment_id', ''),
                task_id=task_id or '',
                task_name=task_name,
                worker_id=worker_id,
                worker_name=assignment.get('worker_name', f"Worker {worker_id}"),
                status=status,
                priority=assignment.get('priority', 'normal'),
                assigned_at=assigned_at,
                started_at=started_at,
                completed_at=completed_at,
                total_urls=total_urls,
                processed_urls=processed_urls,
                success_urls=success_urls,
                failed_urls=failed_urls,
                execution_time_seconds=execution_time,
                avg_response_time_ms=assignment.get('avg_response_time_ms'),
                throughput_per_minute=assignment.get('throughput_per_minute'),
                error_message=assignment.get('error_message'),
                error_count=assignment.get('error_count', 0),
                retry_count=assignment.get('retry_count', 0),
                memory_usage_mb=assignment.get('memory_usage_mb'),
                cpu_usage_percent=assignment.get('cpu_usage_percent')
            )

            return record

        except Exception as e:
            logger.warning(f"Failed to create execution record from assignment: {e}")
            return None

    async def _create_execution_record_from_history(self, execution: Dict[str, Any], worker_id: str) -> Optional[TaskExecutionRecord]:
        """从执行历史创建执行记录"""
        try:
            # 类似于_create_execution_record_from_assignment，但从执行历史数据创建
            task_id = execution.get('task_id')
            task_name = execution.get('task_name', f"历史任务 {task_id}")

            assigned_at = datetime.fromisoformat(execution.get('assigned_at', datetime.now().isoformat()))
            started_at = execution.get('started_at')
            completed_at = execution.get('completed_at')

            if started_at:
                started_at = datetime.fromisoformat(started_at)
            if completed_at:
                completed_at = datetime.fromisoformat(completed_at)

            execution_time = None
            if started_at and completed_at:
                execution_time = (completed_at - started_at).total_seconds()

            record = TaskExecutionRecord(
                execution_id=execution.get('execution_id', f"hist_{uuid.uuid4().hex[:8]}"),
                assignment_id=execution.get('assignment_id', ''),
                task_id=task_id or '',
                task_name=task_name,
                worker_id=worker_id,
                worker_name=execution.get('worker_name', f"Worker {worker_id}"),
                status=execution.get('status', 'completed'),
                priority=execution.get('priority', 'normal'),
                assigned_at=assigned_at,
                started_at=started_at,
                completed_at=completed_at,
                total_urls=execution.get('total_urls', 0),
                processed_urls=execution.get('processed_urls', 0),
                success_urls=execution.get('success_urls', 0),
                failed_urls=execution.get('failed_urls', 0),
                execution_time_seconds=execution_time,
                avg_response_time_ms=execution.get('avg_response_time_ms'),
                throughput_per_minute=execution.get('throughput_per_minute'),
                error_message=execution.get('error_message'),
                error_count=execution.get('error_count', 0),
                retry_count=execution.get('retry_count', 0),
                memory_usage_mb=execution.get('memory_usage_mb'),
                cpu_usage_percent=execution.get('cpu_usage_percent')
            )

            return record

        except Exception as e:
            logger.warning(f"Failed to create execution record from history: {e}")
            return None

    async def _create_execution_record_from_monitoring_task(self, task: Dict[str, Any], worker_id: str) -> Optional[TaskExecutionRecord]:
        """从监控任务创建执行记录"""
        try:
            task_id = task.get('id')
            task_name = task.get('name', f"监控任务 {task_id}")

            # 从任务统计中获取执行信息
            stats = task.get('stats', {})
            last_run = task.get('last_run')

            assigned_at = datetime.now()
            started_at = None
            completed_at = None

            if last_run:
                completed_at = datetime.fromisoformat(last_run)
                # 假设任务执行时间为平均响应时间
                avg_time = stats.get('avg_response_time', 60)
                started_at = completed_at - timedelta(seconds=avg_time)
                assigned_at = started_at - timedelta(minutes=1)  # 假设分配时间比开始时间早1分钟

            # 从任务配置获取URL信息
            total_urls = task.get('total_urls', 0)
            success_rate = stats.get('success_rate', 0.0)
            success_urls = int(total_urls * success_rate / 100) if total_urls > 0 else 0
            failed_urls = total_urls - success_urls

            record = TaskExecutionRecord(
                execution_id=f"monitor_{task_id}_{worker_id}",
                assignment_id=f"assign_{task_id}_{worker_id}",
                task_id=task_id or '',
                task_name=task_name,
                worker_id=worker_id,
                worker_name=f"Worker {worker_id}",
                status='completed' if completed_at else 'pending',
                priority=task.get('config', {}).get('priority', 'normal'),
                assigned_at=assigned_at,
                started_at=started_at,
                completed_at=completed_at,
                total_urls=total_urls,
                processed_urls=success_urls + failed_urls,
                success_urls=success_urls,
                failed_urls=failed_urls,
                execution_time_seconds=stats.get('avg_response_time'),
                avg_response_time_ms=stats.get('avg_response_time', 0) * 1000 if stats.get('avg_response_time') else None,
                throughput_per_minute=None,
                error_message=None,
                error_count=failed_urls,
                retry_count=0,
                memory_usage_mb=None,
                cpu_usage_percent=None
            )

            return record

        except Exception as e:
            logger.warning(f"Failed to create execution record from monitoring task: {e}")
            return None

    async def _worker_executed_task(self, worker_id: str, task_id: str) -> bool:
        """检查Worker是否执行过某个任务"""
        try:
            # 检查Redis中是否有该Worker执行该任务的记录
            execution_key = f"task_execution:worker:{worker_id}:task:{task_id}"
            assignment_key = f"task_assignment:worker:{worker_id}:task:{task_id}"

            return (self.redis_client.exists(execution_key) or
                   self.redis_client.exists(assignment_key))

        except Exception as e:
            logger.warning(f"Failed to check worker task execution: {e}")
            return False

    async def record_task_execution(self, execution_record: TaskExecutionRecord):
        """记录任务执行"""
        try:
            # 保存到Redis
            execution_key = f"task_execution:worker:{execution_record.worker_id}:task:{execution_record.task_id}"
            execution_data = execution_record.dict()
            execution_json = json.dumps(execution_data, default=str, ensure_ascii=False)

            # 设置过期时间为30天
            self.redis_client.setex(execution_key, 30 * 24 * 3600, execution_json)

            # 同时保存到执行历史索引
            history_key = f"task_execution:history:{execution_record.execution_id}"
            self.redis_client.setex(history_key, 30 * 24 * 3600, execution_json)

            # 更新Worker统计
            await self._update_worker_stats_from_execution(execution_record)

            logger.info(f"Recorded task execution: {execution_record.execution_id}")

        except Exception as e:
            logger.error(f"Failed to record task execution: {e}")

    async def _update_worker_stats_from_execution(self, execution_record: TaskExecutionRecord):
        """从执行记录更新Worker统计"""
        try:
            worker = await self.get_worker(execution_record.worker_id)
            if not worker:
                return

            # 更新任务计数
            worker.total_tasks += 1

            if execution_record.status == 'completed':
                worker.completed_tasks += 1
            elif execution_record.status == 'failed':
                worker.failed_tasks += 1

            # 更新成功率
            if worker.total_tasks > 0:
                worker.success_rate = worker.completed_tasks / worker.total_tasks

            # 更新平均执行时间
            if execution_record.execution_time_seconds:
                if worker.avg_task_duration == 0:
                    worker.avg_task_duration = execution_record.execution_time_seconds
                else:
                    # 使用移动平均
                    worker.avg_task_duration = (worker.avg_task_duration * 0.8 +
                                               execution_record.execution_time_seconds * 0.2)

            # 更新最后任务时间
            if execution_record.completed_at:
                worker.last_task_time = execution_record.completed_at

            # 保存更新后的Worker
            await self._save_worker(worker)

        except Exception as e:
            logger.error(f"Failed to update worker stats: {e}")

    async def check_worker_group_compatibility(self, worker_ids: List[str]) -> WorkerGroupCompatibilityCheck:
        """检查Worker组兼容性"""
        try:
            # 获取所有Worker信息
            workers = []
            for worker_id in worker_ids:
                worker = await self.get_worker(worker_id)
                if not worker:
                    return WorkerGroupCompatibilityCheck(
                        worker_ids=worker_ids,
                        is_compatible=False,
                        individual_checks=[],
                        resource_conflicts=[],
                        total_workers=len(worker_ids),
                        valid_workers=0,
                        conflicted_workers=len(worker_ids),
                        recommended_workers=[],
                        alternative_workers=[]
                    )
                workers.append(worker)

            errors = []
            warnings = []
            suggestions = []
            resource_conflicts = []
            config_conflicts = []

            # 检查后端配置冲突
            backend_configs = {}
            for worker in workers:
                backend_id = worker.backend_config_id
                if backend_id in backend_configs:
                    backend_configs[backend_id].append(worker.worker_id)
                else:
                    backend_configs[backend_id] = [worker.worker_id]

            # 检查是否有多个Worker使用同一后端配置
            for backend_id, worker_list in backend_configs.items():
                if len(worker_list) > 1:
                    # 获取后端配置信息
                    from ..services.backend_config_service import backend_config_service
                    backend_config = await backend_config_service.get_config(backend_id)

                    if backend_config:
                        # 计算总并发需求
                        total_concurrent = sum(w.allocated_concurrent for w in workers if w.backend_config_id == backend_id)

                        if total_concurrent > backend_config.max_concurrent:
                            errors.append(
                                f"后端配置 {backend_id} 的总并发需求 ({total_concurrent}) "
                                f"超过最大并发限制 ({backend_config.max_concurrent})"
                            )
                            resource_conflicts.append({
                                "type": "backend_overload",
                                "backend_id": backend_id,
                                "workers": worker_list,
                                "required_concurrent": total_concurrent,
                                "max_concurrent": backend_config.max_concurrent
                            })
                        elif total_concurrent > backend_config.max_concurrent * 0.8:
                            warnings.append(
                                f"后端配置 {backend_id} 的并发使用率较高 "
                                f"({total_concurrent}/{backend_config.max_concurrent})"
                            )

                        # 检查内存使用
                        if backend_config.memory_threshold:
                            estimated_memory = total_concurrent * 100  # 假设每个并发任务使用100MB
                            if estimated_memory > backend_config.memory_threshold:
                                warnings.append(
                                    f"后端配置 {backend_id} 可能存在内存压力 "
                                    f"(预估: {estimated_memory}MB, 阈值: {backend_config.memory_threshold}MB)"
                                )

            # 检查爬取配置冲突
            crawler_configs = set(w.crawler_config_id for w in workers)
            if len(crawler_configs) > 1:
                warnings.append(f"Worker组使用了 {len(crawler_configs)} 种不同的爬取配置，可能影响一致性")
                config_conflicts.append({
                    "type": "crawler_config_mismatch",
                    "configs": list(crawler_configs)
                })

            # 检查优先级冲突
            priorities = set(w.priority for w in workers)
            if len(priorities) > 1:
                suggestions.append("建议统一Worker组的优先级设置以避免调度冲突")

            # 检查Worker状态
            inactive_workers = [w.worker_id for w in workers if w.status != 'active']
            if inactive_workers:
                warnings.append(f"以下Worker不是活跃状态: {', '.join(inactive_workers)}")

            # 生成建议
            if len(workers) > 3:
                suggestions.append("Worker组较大，建议监控资源使用情况")

            if not errors and not warnings:
                suggestions.append("Worker组配置良好，可以安全使用")

            # 生成单个Worker检查结果
            individual_checks = []
            valid_worker_count = 0
            conflicted_worker_count = 0

            for worker in workers:
                worker_errors = []
                worker_warnings = []
                worker_conflicted = []

                # 检查Worker状态
                if worker.status != 'active':
                    worker_warnings.append(f"Worker状态为 {worker.status}")
                else:
                    valid_worker_count += 1

                # 检查资源冲突
                for conflict in resource_conflicts:
                    if worker.worker_id in conflict.get('workers', []):
                        worker_errors.append(f"存在资源冲突: {conflict.get('type', 'unknown')}")
                        worker_conflicted.append(worker.worker_id)
                        conflicted_worker_count += 1

                individual_check = WorkerCompatibilityCheck(
                    worker_id=worker.worker_id,
                    is_compatible=len(worker_errors) == 0,
                    config_exists=True,  # Worker已存在说明配置存在
                    backend_available=worker.status == 'active',
                    resource_conflict=len(worker_conflicted) > 0,
                    errors=worker_errors,
                    warnings=worker_warnings,
                    suggestions=[],
                    conflicted_workers=worker_conflicted
                )
                individual_checks.append(individual_check)

            # 生成推荐和替代Worker列表
            recommended_workers = [w.worker_id for w in workers if w.status == 'active' and w.health_score >= 0.8]
            alternative_workers = []  # 这里可以根据需要实现替代Worker推荐逻辑

            # 判断总体兼容性
            is_compatible = len(errors) == 0

            return WorkerGroupCompatibilityCheck(
                worker_ids=worker_ids,
                is_compatible=is_compatible,
                individual_checks=individual_checks,
                resource_conflicts=resource_conflicts,
                total_workers=len(worker_ids),
                valid_workers=valid_worker_count,
                conflicted_workers=conflicted_worker_count,
                recommended_workers=recommended_workers,
                alternative_workers=alternative_workers
            )

        except Exception as e:
            logger.error(f"Failed to check worker group compatibility: {e}")
            return WorkerGroupCompatibilityCheck(
                worker_ids=worker_ids,
                is_compatible=False,
                individual_checks=[],
                resource_conflicts=[],
                total_workers=len(worker_ids),
                valid_workers=0,
                conflicted_workers=len(worker_ids),
                recommended_workers=[],
                alternative_workers=[]
            )


# 全局服务实例
crawler_worker_service = CrawlerWorkerService()
