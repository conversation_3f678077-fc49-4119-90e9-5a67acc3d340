"""
新架构：爬取配置管理服务
管理纯爬取配置（不包含后端性能配置）
"""

import json
import uuid
import asyncio
import logging
import redis
import os
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
from fastapi import HTTPException

from ..schemas.crawler_config_new import (
    CrawlerConfig, CrawlerConfigCreate, CrawlerConfigUpdate,
    CrawlerConfigSummary, CrawlerConfigStats, CrawlerConfigTemplate,
    CrawlerConfigValidationResult, ConfigStatus
)

logger = logging.getLogger(__name__)


class CrawlerConfigService:
    """爬取配置管理服务"""
    
    def __init__(self):
        # 使用现有的Redis连接模式
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.data_dir = Path("data/crawler_configs")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Redis键模式
        self.keys = {
            'config': 'crawler_config:{}',
            'config_list': 'crawler_config:list',
            'config_by_name': 'crawler_config:name:{}',
            'config_stats': 'crawler_config:stats',
            'config_templates': 'crawler_config:templates'
        }
    
    async def create_config(self, config_data: CrawlerConfigCreate) -> CrawlerConfig:
        """创建爬取配置"""
        try:
            # 检查名称是否已存在
            existing_config = await self.get_config_by_name(config_data.config_name)
            if existing_config:
                raise HTTPException(
                    status_code=400, 
                    detail=f"配置名称 '{config_data.config_name}' 已存在"
                )
            
            # 生成配置ID
            config_id = str(uuid.uuid4())
            now = datetime.now()
            
            # 创建配置对象
            config = CrawlerConfig(
                config_id=config_id,
                config_name=config_data.config_name,
                description=config_data.description,
                browser=config_data.browser,
                crawler=config_data.crawler,
                llm=config_data.llm,
                schema_extraction=config_data.schema_extraction,
                content_processing=config_data.content_processing,
                link_filtering=config_data.link_filtering,
                monitor=config_data.monitor,
                version=config_data.version,
                tags=config_data.tags,
                created_at=now,
                updated_at=now,
                created_by=config_data.created_by
            )
            
            # 保存配置
            await self._save_config(config)
            
            # 添加到配置列表
            self.redis_client.sadd(self.keys['config_list'], config_id)
            
            # 建立名称索引
            self.redis_client.set(
                self.keys['config_by_name'].format(config_data.config_name), 
                config_id
            )
            
            logger.info(f"Created crawler config: {config_id} ({config_data.config_name})")
            return config
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to create crawler config: {e}")
            raise HTTPException(status_code=500, detail=f"创建配置失败: {str(e)}")
    
    async def get_config(self, config_id: str) -> Optional[CrawlerConfig]:
        """获取爬取配置"""
        try:
            # 从Redis获取
            config_data = self.redis_client.get(self.keys['config'].format(config_id))
            if config_data:
                config_dict = json.loads(config_data)
                return CrawlerConfig(**config_dict)
            
            # 从文件获取
            config_file = self.data_dir / f"{config_id}.json"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
                    config = CrawlerConfig(**config_dict)
                    # 同步到Redis
                    await self._save_config(config)
                    return config
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get crawler config {config_id}: {e}")
            return None
    
    async def get_config_by_name(self, config_name: str) -> Optional[CrawlerConfig]:
        """根据名称获取配置"""
        try:
            config_id = self.redis_client.get(self.keys['config_by_name'].format(config_name))
            if config_id:
                config_id_str = config_id if isinstance(config_id, str) else config_id.decode()
                return await self.get_config(config_id_str)
            return None
        except Exception as e:
            logger.error(f"Failed to get config by name {config_name}: {e}")
            return None
    
    async def update_config(self, config_id: str, update_data: CrawlerConfigUpdate) -> Optional[CrawlerConfig]:
        """更新爬取配置"""
        try:
            # 获取现有配置
            config = await self.get_config(config_id)
            if not config:
                raise HTTPException(status_code=404, detail="配置不存在")
            
            # 检查名称冲突
            if update_data.config_name and update_data.config_name != config.config_name:
                existing_config = await self.get_config_by_name(update_data.config_name)
                if existing_config and existing_config.config_id != config_id:
                    raise HTTPException(
                        status_code=400, 
                        detail=f"配置名称 '{update_data.config_name}' 已存在"
                    )
            
            # 更新配置
            update_dict = update_data.dict(exclude_unset=True)
            for field, value in update_dict.items():
                if hasattr(config, field):
                    setattr(config, field, value)
            
            config.updated_at = datetime.now()
            
            # 保存更新后的配置
            await self._save_config(config)
            
            # 更新名称索引
            if update_data.config_name and update_data.config_name != config.config_name:
                # 删除旧索引
                self.redis_client.delete(self.keys['config_by_name'].format(config.config_name))
                # 创建新索引
                self.redis_client.set(
                    self.keys['config_by_name'].format(update_data.config_name), 
                    config_id
                )
            
            logger.info(f"Updated crawler config: {config_id}")
            return config
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to update crawler config {config_id}: {e}")
            raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")
    
    async def delete_config(self, config_id: str) -> bool:
        """删除爬取配置"""
        try:
            # 获取配置信息
            config = await self.get_config(config_id)
            if not config:
                raise HTTPException(status_code=404, detail="配置不存在")
            
            # 检查是否被Worker使用
            # TODO: 实现Worker依赖检查
            
            # 从Redis删除
            self.redis_client.delete(self.keys['config'].format(config_id))
            self.redis_client.srem(self.keys['config_list'], config_id)
            self.redis_client.delete(self.keys['config_by_name'].format(config.config_name))
            
            # 删除文件
            config_file = self.data_dir / f"{config_id}.json"
            if config_file.exists():
                config_file.unlink()
            
            logger.info(f"Deleted crawler config: {config_id}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to delete crawler config {config_id}: {e}")
            return False
    
    async def list_configs(
        self, 
        status: Optional[ConfigStatus] = None,
        tags: Optional[List[str]] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[CrawlerConfigSummary]:
        """获取配置列表"""
        try:
            # 获取所有配置ID
            config_ids = self.redis_client.smembers(self.keys['config_list'])
            if not config_ids:
                return []
            
            # 获取配置详情
            configs = []
            for config_id in config_ids:
                config_id_str = config_id if isinstance(config_id, str) else config_id.decode()
                config = await self.get_config(config_id_str)
                if config:
                    configs.append(config)
            
            # 过滤
            if status:
                configs = [c for c in configs if c.status == status]
            
            if tags:
                configs = [c for c in configs if any(tag in c.tags for tag in tags)]
            
            # 排序（按更新时间倒序）
            configs.sort(key=lambda x: x.updated_at or x.created_at or datetime.min, reverse=True)
            
            # 分页
            configs = configs[offset:offset + limit]
            
            # 转换为摘要
            summaries = [CrawlerConfigSummary.from_config(config) for config in configs]
            
            return summaries
            
        except Exception as e:
            logger.error(f"Failed to list crawler configs: {e}")
            return []
    
    async def get_config_stats(self) -> CrawlerConfigStats:
        """获取配置统计信息"""
        try:
            # 获取所有配置
            configs = await self.list_configs(limit=1000)
            
            # 统计信息
            total_configs = len(configs)
            active_configs = len([c for c in configs if c.status == ConfigStatus.ACTIVE])
            inactive_configs = len([c for c in configs if c.status == ConfigStatus.INACTIVE])
            draft_configs = len([c for c in configs if c.status == ConfigStatus.DRAFT])
            archived_configs = len([c for c in configs if c.status == ConfigStatus.ARCHIVED])
            
            total_usage = sum(c.usage_count for c in configs)
            avg_usage = total_usage / total_configs if total_configs > 0 else 0
            
            # 最常用配置
            most_used = max(configs, key=lambda x: x.usage_count) if configs else None
            
            # 最近创建和使用的配置
            recently_created = sorted(configs, key=lambda x: x.created_at or datetime.min, reverse=True)[:5]
            recently_used = sorted(
                [c for c in configs if c.last_used], 
                key=lambda x: x.last_used, 
                reverse=True
            )[:5]
            
            # 特征统计
            browser_stats = {}
            llm_provider_stats = {}
            llm_model_stats = {}
            
            for config in configs:
                # 浏览器统计
                headless_key = f"headless_{config.browser_headless}"
                browser_stats[headless_key] = browser_stats.get(headless_key, 0) + 1
                
                # LLM统计
                llm_provider_stats[config.llm_provider] = llm_provider_stats.get(config.llm_provider, 0) + 1
                llm_model_stats[config.llm_model] = llm_model_stats.get(config.llm_model, 0) + 1
            
            return CrawlerConfigStats(
                total_configs=total_configs,
                active_configs=active_configs,
                inactive_configs=inactive_configs,
                draft_configs=draft_configs,
                archived_configs=archived_configs,
                total_usage=total_usage,
                avg_usage_per_config=avg_usage,
                most_used_config=most_used,
                recently_created=recently_created,
                recently_used=recently_used,
                browser_stats=browser_stats,
                llm_provider_stats=llm_provider_stats,
                llm_model_stats=llm_model_stats
            )
            
        except Exception as e:
            logger.error(f"Failed to get config stats: {e}")
            return CrawlerConfigStats(
                total_configs=0,
                active_configs=0,
                inactive_configs=0,
                draft_configs=0,
                archived_configs=0,
                total_usage=0,
                avg_usage_per_config=0.0,
                recently_created=[],
                recently_used=[],
                browser_stats={},
                llm_provider_stats={},
                llm_model_stats={}
            )
    
    async def validate_config(self, config: CrawlerConfig) -> CrawlerConfigValidationResult:
        """验证配置"""
        try:
            result = CrawlerConfigValidationResult(is_valid=True)
            
            # 基本验证
            if not config.config_name or not config.config_name.strip():
                result.errors.append("配置名称不能为空")
                result.is_valid = False
            
            # 浏览器配置验证
            if config.browser.timeout <= 0:
                result.errors.append("浏览器超时时间必须大于0")
                result.is_valid = False
            
            # LLM配置验证
            if not config.llm.provider:
                result.errors.append("LLM提供商不能为空")
                result.is_valid = False
            
            if not config.llm.model:
                result.errors.append("LLM模型不能为空")
                result.is_valid = False
            
            # 添加建议
            if config.browser.headless is False:
                result.suggestions.append("建议在生产环境中使用无头模式")
            
            if config.llm.temperature > 0.8:
                result.warnings.append("LLM温度设置较高，可能影响结果稳定性")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to validate config: {e}")
            return CrawlerConfigValidationResult(
                is_valid=False,
                errors=[f"验证过程出错: {str(e)}"]
            )
    
    async def _save_config(self, config: CrawlerConfig):
        """保存配置到Redis和文件"""
        try:
            config_dict = config.dict()
            config_json = json.dumps(config_dict, default=str, ensure_ascii=False)
            
            # 保存到Redis
            self.redis_client.set(
                self.keys['config'].format(config.config_id), 
                config_json
            )
            
            # 保存到文件
            config_file = self.data_dir / f"{config.config_id}.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False, default=str)
            
        except Exception as e:
            logger.error(f"Failed to save config {config.config_id}: {e}")
            raise


# 全局服务实例
crawler_config_service = CrawlerConfigService()
