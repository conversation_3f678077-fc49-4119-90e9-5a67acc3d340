/**
 * 监控任务API服务
 * 
 * 提供监控任务的CRUD操作和管理功能
 */

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000';

// 类型定义
export interface ScheduleConfig {
  type: 'once' | 'daily' | 'weekly' | 'hourly' | 'custom';
  enabled?: boolean;
  time?: string;
  start_time?: string;
  days?: number[];
  interval?: number;
  timezone: string;
  cron_expression?: string;
  end_time?: string;
  max_runs?: number;
  enable_random_delay?: boolean;
  random_delay_min?: number;
  random_delay_max?: number;
}

export interface TaskConfig {
  platform: string;
  priority: 'high' | 'medium' | 'low' | 'normal' | 'urgent';
  retry_count: number;
  timeout: number;
  batch_size: number;
  concurrent_limit: number;
  enable_notifications: boolean;
  notification_config?: Record<string, any>;
}

export interface TaskUrl {
  id: string;
  url: string;
  platform: string;
  status: string;
  source_file: string;
  added_at: string;
  last_check?: string;
  check_count: number;
  error_count: number;
  metadata?: Record<string, any>;
}

export interface TaskStats {
  total_runs: number;
  success_rate: number;
  avg_response_time: number;
  last_success_time?: string;
}

export interface MonitoringTask {
  id: string;
  name: string;
  description?: string;
  tags?: string[];
  schedule: ScheduleConfig;
  config: TaskConfig;
  total_urls: number;
  active_urls: number;
  status: 'draft' | 'active' | 'paused' | 'stopped';
  is_running: boolean;
  last_run?: string;
  next_run?: string;
  stats: TaskStats;
  created_at: string;
  updated_at: string;
  source?: 'url_pool' | 'manual' | 'api' | 'excel';
  source_info?: {
    url_count?: number;
    created_from?: string;
    [key: string]: any;
  };
}

export interface MonitoringTaskCreate {
  name: string;
  description?: string;
  schedule: ScheduleConfig;
  config: TaskConfig;
  urls?: string[];
}

export interface MonitoringTaskUpdate {
  name?: string;
  description?: string;
  schedule?: ScheduleConfig;
  config?: TaskConfig;
  status?: 'draft' | 'active' | 'paused' | 'stopped';
}

export interface MonitoringTaskListResponse {
  success: boolean;
  data: MonitoringTask[];
  total: number;
}

export interface MonitoringTaskDetailResponse {
  success: boolean;
  data: MonitoringTask;
}

export interface ApiResponse {
  success: boolean;
  message: string;
}

/**
 * 获取监控任务列表 - 增强版
 */
export const getMonitoringTasks = async (params?: {
  status?: string;
  platform?: string;
  source?: string;
  search?: string;
  sort_by?: string;
  sort_order?: string;
  page?: number;
  page_size?: number;
}): Promise<MonitoringTaskListResponse> => {
  const searchParams = new URLSearchParams();

  // 筛选参数
  if (params?.status) {
    searchParams.append('status', params.status);
  }
  if (params?.platform) {
    searchParams.append('platform', params.platform);
  }
  if (params?.source) {
    searchParams.append('source', params.source);
  }
  if (params?.search) {
    searchParams.append('search', params.search);
  }

  // 排序参数
  if (params?.sort_by) {
    searchParams.append('sort_by', params.sort_by);
  }
  if (params?.sort_order) {
    searchParams.append('sort_order', params.sort_order);
  }

  // 分页参数
  if (params?.page) {
    searchParams.append('page', params.page.toString());
  }
  if (params?.page_size) {
    searchParams.append('page_size', params.page_size.toString());
  }

  const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/?${searchParams}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 创建监控任务
 */
export const createMonitoringTask = async (taskData: MonitoringTaskCreate): Promise<MonitoringTaskDetailResponse> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(taskData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 获取监控任务详情
 */
export const getMonitoringTask = async (taskId: string): Promise<MonitoringTaskDetailResponse> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 更新监控任务
 */
export const updateMonitoringTask = async (
  taskId: string,
  updateData: MonitoringTaskUpdate
): Promise<MonitoringTaskDetailResponse> => {
  console.log('=== API调用调试信息 ===');
  console.log('updateMonitoringTask 调用参数:');
  console.log('  taskId:', taskId);
  console.log('  updateData:', updateData);
  console.log('  updateData JSON:', JSON.stringify(updateData, null, 2));

  const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(updateData),
  });

  console.log('API响应:');
  console.log('  status:', response.status);
  console.log('  statusText:', response.statusText);
  console.log('  headers:', Object.fromEntries(response.headers.entries()));

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    console.error('API返回错误:');
    console.error('  status:', response.status);
    console.error('  errorData:', errorData);
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  const responseData = await response.json();
  console.log('API成功响应数据:', responseData);
  return responseData;
};

/**
 * 删除监控任务
 */
export const deleteMonitoringTask = async (taskId: string): Promise<ApiResponse> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 启动监控任务
 */
export const startMonitoringTask = async (taskId: string): Promise<ApiResponse> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}/start`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 暂停监控任务
 */
export const pauseMonitoringTask = async (taskId: string): Promise<ApiResponse> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}/pause`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 获取支持的平台列表
 */
export const getSupportedPlatforms = async (): Promise<{ platforms: string[] }> => {
  // 临时返回静态数据，后续可以从API获取
  return {
    platforms: ['taobao', 'tmall', 'jd', 'pdd']
  };
};



/**
 * 从Excel文件解析URL并添加到监控任务
 */
export const addExcelUrlsToTask = async (taskId: string, file: File): Promise<ApiResponse> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}/urls/excel`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 获取任务关联的URL列表
 */
export const getTaskUrls = async (taskId: string, params?: {
  status?: string;
  page?: number;
  page_size?: number;
}): Promise<{
  success: boolean;
  data: TaskUrl[];
  total: number;
  page: number;
  page_size: number;
}> => {
  const searchParams = new URLSearchParams();

  if (params?.status) {
    searchParams.append('status', params.status);
  }
  if (params?.page) {
    searchParams.append('page', params.page.toString());
  }
  if (params?.page_size) {
    searchParams.append('page_size', params.page_size.toString());
  }

  const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}/urls?${searchParams}`);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 更新任务关联的URL
 */
export const updateTaskUrls = async (taskId: string, urls: string[]): Promise<{
  success: boolean;
  added_count: number;
  duplicate_count: number;
  message: string;
}> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}/urls`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ urls }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 获取任务执行历史
 */
export const getTaskExecutionHistory = async (taskId: string, params?: {
  page?: number;
  page_size?: number;
}): Promise<{
  success: boolean;
  data: any[];
  total: number;
  page: number;
  page_size: number;
}> => {
  const searchParams = new URLSearchParams();

  if (params?.page) {
    searchParams.append('page', params.page.toString());
  }
  if (params?.page_size) {
    searchParams.append('page_size', params.page_size.toString());
  }

  const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}/history?${searchParams}`);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 获取任务统计信息
 */
export const getTaskStats = async (taskId: string): Promise<{
  success: boolean;
  data: any;
}> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}/stats`);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 手动立即执行任务
 */
export const executeTaskManually = async (taskId: string): Promise<{
  success: boolean;
  execution_id: string;
  message: string;
  url_count: number;
}> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}/execute`, {
    method: 'POST',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 终止正在执行的任务
 */
export const terminateTaskExecution = async (taskId: string): Promise<{
  success: boolean;
  message: string;
}> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}/terminate`, {
    method: 'POST',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};



/**
 * 重启失败的任务
 */
export const restartFailedTask = async (taskId: string): Promise<{
  success: boolean;
  execution_id: string;
  message: string;
  url_count: number;
}> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}/restart`, {
    method: 'POST',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 获取任务执行状态
 */
export const getTaskExecutionStatus = async (taskId: string): Promise<{
  success: boolean;
  data: any;
}> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}/execution-status`);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// ==================== URL管理API ====================

export interface UrlOperationResponse {
  success: boolean;
  message: string;
  affected_count: number;
  failed_urls: string[];
}

// 批量更新URL状态
export const updateUrlsBatchStatus = async (
  taskId: string,
  urlIds: string[],
  status: 'active' | 'disabled'
): Promise<UrlOperationResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}/urls/batch-status`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url_ids: urlIds,
        status: status
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating URLs batch status:', error);
    throw error;
  }
};

// 更新单个URL状态
export const updateUrlStatus = async (
  taskId: string,
  urlId: string,
  status: 'active' | 'disabled'
): Promise<UrlOperationResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}/urls/${urlId}/status`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        status: status
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating URL status:', error);
    throw error;
  }
};

// 批量移除URL
export const removeUrlsBatch = async (
  taskId: string,
  urlIds: string[]
): Promise<UrlOperationResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}/urls/batch`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url_ids: urlIds
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error removing URLs batch:', error);
    throw error;
  }
};

// 移除单个URL
export const removeUrl = async (
  taskId: string,
  urlId: string
): Promise<UrlOperationResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}/urls/${urlId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error removing URL:', error);
    throw error;
  }
};

// 添加URL到任务
export const addUrlsToTask = async (
  taskId: string,
  urlIds: string[]
): Promise<UrlOperationResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/monitoring-tasks/${taskId}/urls/add`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url_ids: urlIds
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error adding URLs to task:', error);
    throw error;
  }
};
