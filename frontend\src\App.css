/* 全局样式重置和优化 */
* {
  box-sizing: border-box;
}

/* 抑制Microsoft Edge的-ms-high-contrast废弃警告 */
@media (forced-colors: active) {
  /* 使用新的Forced Colors Mode标准替代-ms-high-contrast */
  .ant-btn {
    forced-color-adjust: auto;
  }

  .ant-input {
    forced-color-adjust: auto;
  }

  .ant-card {
    forced-color-adjust: auto;
  }
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: #f5f5f5;
  color: #333333;
  line-height: 1.6;
}

/* 改善文本可读性 */
.ant-typography {
  color: #333333 !important;
}

.ant-typography.ant-typography-secondary {
  color: #666666 !important;
}

/* 卡片全局样式优化 */
.ant-card {
  background: #ffffff !important;
  border: 1px solid #f0f0f0 !important;
}

.ant-card-head {
  border-bottom: 1px solid #f5f5f5 !important;
}

.ant-card-head-title {
  color: #1a1a1a !important;
  font-weight: 600 !important;
}

/* 按钮样式优化 */
.ant-btn-primary {
  background: #1890ff !important;
  border-color: #1890ff !important;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2) !important;
}

.ant-btn-primary:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3) !important;
}

/* 统计数字样式优化 */
.ant-statistic-content-value {
  color: #1a1a1a !important;
  font-weight: 600 !important;
}

.ant-statistic-title {
  color: #666666 !important;
  font-weight: 500 !important;
}

/* 菜单样式优化 */
.ant-menu-item-selected {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
}

.ant-menu-item-selected .anticon {
  color: #1890ff !important;
}

/* 夜间模式全局样式 */
.app-layout.dark body {
  background-color: #1f1f1f !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-typography {
  color: #ffffff !important;
}

.app-layout.dark .ant-typography.ant-typography-secondary {
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-card {
  background: #262626 !important;
  border: 1px solid #303030 !important;
}

.app-layout.dark .ant-card-head {
  border-bottom: 1px solid #303030 !important;
}

.app-layout.dark .ant-card-head-title {
  color: #ffffff !important;
  font-weight: 600 !important;
}

.app-layout.dark .ant-btn-primary {
  background: #1890ff !important;
  border-color: #1890ff !important;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2) !important;
}

.app-layout.dark .ant-btn-primary:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3) !important;
}

.app-layout.dark .ant-statistic-content-value {
  color: #ffffff !important;
  font-weight: 600 !important;
}

.app-layout.dark .ant-statistic-title {
  color: #d9d9d9 !important;
  font-weight: 500 !important;
}

.app-layout.dark .ant-menu-item-selected {
  background-color: #1890ff !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-menu-item-selected .anticon {
  color: #ffffff !important;
}

.app-layout.dark .ant-table {
  background: #262626 !important;
}

.app-layout.dark .ant-table-thead > tr > th {
  background: #303030 !important;
  color: #ffffff !important;
  border-bottom: 1px solid #434343 !important;
}

.app-layout.dark .ant-table-tbody > tr > td {
  background: #262626 !important;
  color: #ffffff !important;
  border-bottom: 1px solid #303030 !important;
}

.app-layout.dark .ant-table-tbody > tr:hover > td {
  background: #303030 !important;
}

.app-layout.dark .ant-input {
  background: #262626 !important;
  border-color: #434343 !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-input:hover {
  border-color: #1890ff !important;
}

.app-layout.dark .ant-input:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.app-layout.dark .ant-select-selector {
  background: #262626 !important;
  border-color: #434343 !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-select-selection-item {
  color: #ffffff !important;
}

.app-layout.dark .ant-tabs-tab {
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-tabs-tab-active {
  color: #1890ff !important;
}

.app-layout.dark .ant-tabs-ink-bar {
  background: #1890ff !important;
}

.app-layout.dark .ant-upload-drag {
  background: #262626 !important;
  border-color: #434343 !important;
}

.app-layout.dark .ant-upload-drag:hover {
  border-color: #1890ff !important;
  background: #303030 !important;
}

.app-layout.dark .ant-upload-text {
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-upload-hint {
  color: #8c8c8c !important;
}

.app-layout.dark .ant-upload-drag-icon {
  color: #595959 !important;
}

.app-layout.dark .ant-upload-drag-icon .anticon {
  color: #595959 !important;
}

.app-layout.dark .ant-progress-bg {
  background: #1890ff !important;
}

.app-layout.dark .ant-progress-inner {
  background: #434343 !important;
}

.app-layout.dark .ant-tag {
  background: #434343 !important;
  border-color: #434343 !important;
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-tag-blue {
  background: #1890ff !important;
  border-color: #1890ff !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-tag-green {
  background: #52c41a !important;
  border-color: #52c41a !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-tag-red {
  background: #ff4d4f !important;
  border-color: #ff4d4f !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-tag-orange {
  background: #faad14 !important;
  border-color: #faad14 !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-alert {
  background: #262626 !important;
  border-color: #434343 !important;
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-alert-success {
  background: #162312 !important;
  border-color: #274916 !important;
}

.app-layout.dark .ant-alert-info {
  background: #111a2c !important;
  border-color: #153450 !important;
}

.app-layout.dark .ant-alert-warning {
  background: #2b2111 !important;
  border-color: #613400 !important;
}

.app-layout.dark .ant-alert-error {
  background: #2a1215 !important;
  border-color: #58181c !important;
}

.app-layout.dark .ant-alert-message {
  color: #ffffff !important;
}

.app-layout.dark .ant-alert-description {
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-drawer {
  background: #1f1f1f !important;
}

.app-layout.dark .ant-drawer-header {
  background: #262626 !important;
  border-bottom-color: #434343 !important;
}

.app-layout.dark .ant-drawer-title {
  color: #ffffff !important;
}

.app-layout.dark .ant-drawer-body {
  background: #1f1f1f !important;
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-drawer-close {
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-drawer-close:hover {
  color: #ffffff !important;
}

/* 表单组件夜间模式 */
.app-layout.dark .ant-form-item-label > label {
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-form-item-label > label.ant-form-item-required::before {
  color: #ff4d4f !important;
}

.app-layout.dark .ant-form-item-explain {
  color: #8c8c8c !important;
}

.app-layout.dark .ant-form-item-extra {
  color: #8c8c8c !important;
}

.app-layout.dark .ant-input-number {
  background: #262626 !important;
  border-color: #434343 !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-input-number:hover {
  border-color: #1890ff !important;
}

.app-layout.dark .ant-input-number:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.app-layout.dark .ant-input-number-input {
  color: #ffffff !important;
}

.app-layout.dark .ant-input-password {
  background: #262626 !important;
  border-color: #434343 !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-input-password:hover {
  border-color: #1890ff !important;
}

.app-layout.dark .ant-input-password:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.app-layout.dark .ant-input-password-icon {
  color: #8c8c8c !important;
}

.app-layout.dark .ant-input-password-icon:hover {
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-switch {
  background: #434343 !important;
}

.app-layout.dark .ant-switch-checked {
  background: #1890ff !important;
}

.app-layout.dark .ant-switch:hover:not(.ant-switch-disabled) {
  background: #595959 !important;
}

.app-layout.dark .ant-switch-checked:hover:not(.ant-switch-disabled) {
  background: #40a9ff !important;
}

.app-layout.dark .ant-radio-wrapper {
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-radio {
  border-color: #434343 !important;
}

.app-layout.dark .ant-radio:hover {
  border-color: #1890ff !important;
}

.app-layout.dark .ant-radio-checked {
  border-color: #1890ff !important;
}

.app-layout.dark .ant-radio-inner {
  background: #262626 !important;
  border-color: #434343 !important;
}

.app-layout.dark .ant-radio-checked .ant-radio-inner {
  background: #1890ff !important;
  border-color: #1890ff !important;
}

.app-layout.dark .ant-checkbox-wrapper {
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-checkbox {
  border-color: #434343 !important;
}

.app-layout.dark .ant-checkbox:hover {
  border-color: #1890ff !important;
}

.app-layout.dark .ant-checkbox-checked {
  border-color: #1890ff !important;
}

.app-layout.dark .ant-checkbox-inner {
  background: #262626 !important;
  border-color: #434343 !important;
}

.app-layout.dark .ant-checkbox-checked .ant-checkbox-inner {
  background: #1890ff !important;
  border-color: #1890ff !important;
}

/* Select组件夜间模式增强 */
.app-layout.dark .ant-select-dropdown {
  background: #262626 !important;
  border-color: #434343 !important;
}

.app-layout.dark .ant-select-item {
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-select-item:hover {
  background: #303030 !important;
}

.app-layout.dark .ant-select-item-option-selected {
  background: #1890ff !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-select-item-option-active {
  background: #303030 !important;
}

.app-layout.dark .ant-select-arrow {
  color: #8c8c8c !important;
}

.app-layout.dark .ant-select-clear {
  color: #8c8c8c !important;
}

.app-layout.dark .ant-select-clear:hover {
  color: #d9d9d9 !important;
}

/* DatePicker组件夜间模式 */
.app-layout.dark .ant-picker {
  background: #262626 !important;
  border-color: #434343 !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-picker:hover {
  border-color: #1890ff !important;
}

.app-layout.dark .ant-picker:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.app-layout.dark .ant-picker-input > input {
  color: #ffffff !important;
}

.app-layout.dark .ant-picker-input > input::placeholder {
  color: #8c8c8c !important;
}

.app-layout.dark .ant-picker-suffix {
  color: #8c8c8c !important;
}

.app-layout.dark .ant-picker-clear {
  color: #8c8c8c !important;
}

.app-layout.dark .ant-picker-clear:hover {
  color: #d9d9d9 !important;
}

/* Dropdown和Popover组件夜间模式 */
.app-layout.dark .ant-dropdown {
  background: #262626 !important;
}

.app-layout.dark .ant-dropdown-menu {
  background: #262626 !important;
  border-color: #434343 !important;
}

.app-layout.dark .ant-dropdown-menu-item {
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-dropdown-menu-item:hover {
  background: #303030 !important;
}

.app-layout.dark .ant-popover {
  background: #262626 !important;
}

.app-layout.dark .ant-popover-inner {
  background: #262626 !important;
  border-color: #434343 !important;
}

.app-layout.dark .ant-popover-title {
  color: #ffffff !important;
  border-bottom-color: #434343 !important;
}

.app-layout.dark .ant-popover-inner-content {
  color: #d9d9d9 !important;
}

/* Tooltip组件夜间模式 */
.app-layout.dark .ant-tooltip {
  background: #434343 !important;
}

.app-layout.dark .ant-tooltip-inner {
  background: #434343 !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-tooltip-arrow::before {
  background: #434343 !important;
}

/* 确保所有内容区域都正确适配夜间模式 */
.app-layout.dark .ant-layout-content,
.app-layout.dark .app-content,
.app-layout.dark [class*="css-"][class*="ant-layout-content"] {
  background: #1f1f1f !important;
  color: #ffffff !important;
}

/* 确保所有动态生成的CSS类也被覆盖 */
.app-layout.dark [class*="css-"][class*="do-not-override"] {
  background: #1f1f1f !important;
  color: #ffffff !important;
}

/* 确保页面容器背景正确 */
.app-layout.dark {
  background: #1f1f1f !important;
}

.app-layout.dark .ant-layout {
  background: #1f1f1f !important;
}

/* 修复所有页面标题下方的黑色条框问题 */
.app-layout.dark .ant-typography-title {
  color: #ffffff !important;
  border-bottom: none !important;
  background: transparent !important;
}

.app-layout.dark .ant-typography-paragraph {
  color: #d9d9d9 !important;
  background: transparent !important;
}

.app-layout.dark .ant-typography {
  color: #d9d9d9 !important;
  background: transparent !important;
}

/* 强制移除所有文字元素的背景色 */
.app-layout.dark h1,
.app-layout.dark h2,
.app-layout.dark h3,
.app-layout.dark h4,
.app-layout.dark h5,
.app-layout.dark h6 {
  background: transparent !important;
  color: #ffffff !important;
}

.app-layout.dark p,
.app-layout.dark span:not(.ant-tag):not(.ant-badge):not(.ant-statistic-content-value) {
  background: transparent !important;
  color: #d9d9d9 !important;
}

.app-layout.dark div:not([class*="ant-"]):not([class*="stats-"]):not([class*="card"]) {
  background: transparent !important;
}

/* 特别处理页面头部区域 */
.app-layout.dark [class*="header"] * {
  background: transparent !important;
}

.app-layout.dark [class*="header"] h1,
.app-layout.dark [class*="header"] h2,
.app-layout.dark [class*="header"] h3 {
  color: #ffffff !important;
  background: transparent !important;
}

.app-layout.dark [class*="header"] p,
.app-layout.dark [class*="header"] span,
.app-layout.dark [class*="header"] div {
  color: #d9d9d9 !important;
  background: transparent !important;
}

/* 修复所有输入框占位符文字 */
.app-layout.dark .ant-input::placeholder,
.app-layout.dark .ant-input-affix-wrapper input::placeholder,
.app-layout.dark .ant-select-selection-placeholder {
  color: #8c8c8c !important;
}

/* 修复搜索框 */
.app-layout.dark .ant-input-search {
  background: #262626 !important;
}

.app-layout.dark .ant-input-search .ant-input {
  background: #262626 !important;
  border-color: #434343 !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-input-search .ant-input-search-button {
  background: #1890ff !important;
  border-color: #1890ff !important;
}

.app-layout.dark .ant-input-search .ant-input-search-button:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* 强制修复所有页面的黑色方块问题 */
.app-layout.dark .ant-row {
  background: transparent !important;
}

.app-layout.dark .ant-col {
  background: transparent !important;
}

.app-layout.dark .ant-form-item {
  background: transparent !important;
}

.app-layout.dark .ant-form-item-label {
  background: transparent !important;
}

.app-layout.dark .ant-form-item-control {
  background: transparent !important;
}

.app-layout.dark .ant-form-item-control-input {
  background: transparent !important;
}

.app-layout.dark .ant-form-item-control-input-content {
  background: transparent !important;
}

/* 确保所有容器元素背景透明 */
.app-layout.dark [class*="section"],
.app-layout.dark [class*="container"],
.app-layout.dark [class*="wrapper"] {
  background: transparent !important;
}

/* 只有特定的卡片组件才有背景色 */
.app-layout.dark .ant-card {
  background: #262626 !important;
  border-color: #303030 !important;
}

.app-layout.dark .ant-card-head {
  background: #262626 !important;
  border-bottom-color: #303030 !important;
}

.app-layout.dark .ant-card-body {
  background: #262626 !important;
}

/* 修复统计卡片文字的黑色方块问题 */
.app-layout.dark .ant-statistic {
  background: transparent !important;
}

.app-layout.dark .ant-statistic-title {
  background: transparent !important;
  color: #d9d9d9 !important;
}

.app-layout.dark .ant-statistic-content {
  background: transparent !important;
}

.app-layout.dark .ant-statistic-content-value {
  background: transparent !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-statistic-content-suffix {
  background: transparent !important;
  color: #ffffff !important;
}

.app-layout.dark .ant-statistic-content-prefix {
  background: transparent !important;
}

/* 强制移除所有统计组件内部元素的背景 */
.app-layout.dark .ant-statistic * {
  background: transparent !important;
}

/* 修复overview-card和stats-card的文字背景 */
.app-layout.dark .overview-card * {
  background: transparent !important;
}

.app-layout.dark .stats-card * {
  background: transparent !important;
}

.app-layout.dark .overview-card .ant-statistic-title {
  color: #d9d9d9 !important;
}

.app-layout.dark .overview-card .ant-statistic-content-value {
  color: #ffffff !important;
}

.app-layout.dark .stats-card .ant-statistic-title {
  color: #d9d9d9 !important;
}

.app-layout.dark .stats-card .ant-statistic-content-value {
  color: #ffffff !important;
}

/* 强制修复所有可能的统计卡片组合 */
.app-layout.dark [class*="stats-card"] * {
  background: transparent !important;
}

.app-layout.dark [class*="stats-card"] .ant-statistic-title {
  color: #d9d9d9 !important;
  background: transparent !important;
}

.app-layout.dark [class*="stats-card"] .ant-statistic-content-value {
  color: #ffffff !important;
  background: transparent !important;
}

.app-layout.dark .stats-cards * {
  background: transparent !important;
}

.app-layout.dark .stats-cards .ant-card {
  background: #262626 !important;
}

.app-layout.dark .stats-cards .ant-card-body {
  background: #262626 !important;
}

/* 针对日志页面的特定修复 */
.app-layout.dark .logs-stats * {
  background: transparent !important;
}

.app-layout.dark .logs-stats .ant-card {
  background: #262626 !important;
}

.app-layout.dark .logs-stats .ant-card-body {
  background: #262626 !important;
}

/* 最强制的统计文字修复 - 覆盖所有可能的情况 */
.app-layout.dark span,
.app-layout.dark div:not(.ant-card):not(.ant-card-body):not(.ant-card-head) {
  background: transparent !important;
}

.app-layout.dark .ant-statistic-title,
.app-layout.dark .ant-statistic-content,
.app-layout.dark .ant-statistic-content-value,
.app-layout.dark .ant-statistic-content-suffix,
.app-layout.dark .ant-statistic-content-prefix {
  background: transparent !important;
}

/* 针对所有可能的文字元素 */
.app-layout.dark [class*="ant-statistic"] {
  background: transparent !important;
}

.app-layout.dark [class*="ant-statistic"] * {
  background: transparent !important;
}

/* 使用属性选择器强制覆盖 */
.app-layout.dark [class*="statistic"] {
  background: transparent !important;
}

.app-layout.dark [class*="statistic"] * {
  background: transparent !important;
}

/* 最后的杀手锏 - 针对所有文字内容 */
.app-layout.dark * {
  background: transparent !important;
}

.app-layout.dark .ant-card,
.app-layout.dark .ant-card-head,
.app-layout.dark .ant-card-body,
.app-layout.dark .ant-input,
.app-layout.dark .ant-select-selector,
.app-layout.dark .ant-btn,
.app-layout.dark .ant-menu,
.app-layout.dark .app-header,
.app-layout.dark .app-sider,
.app-layout.dark .ant-layout-sider {
  background: #262626 !important;
}

/* 表格样式优化 */
.ant-table-thead > tr > th {
  background: #fafafa !important;
  color: #333333 !important;
  font-weight: 600 !important;
  border-bottom: 2px solid #f0f0f0 !important;
}

.ant-table-tbody > tr > td {
  color: #333333 !important;
  border-bottom: 1px solid #f5f5f5 !important;
}

.ant-table-tbody > tr:hover > td {
  background: #f8f9fa !important;
}
