#!/usr/bin/env python3
"""
测试Worker任务分配修复效果的完整测试脚本
"""

import requests
import json
from datetime import datetime

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def test_forward_query():
    """测试正向查询：任务 → Worker"""
    
    print("🔍 测试正向查询（任务 → Worker）...")
    
    task_id = "success"
    
    try:
        response = requests.get(f"{BASE_URL}/crawler-workers/task/{task_id}/assigned-workers")
        if response.status_code == 200:
            workers = response.json()
            print(f"✅ 任务 '{task_id}' 分配的Worker数量: {len(workers)}")
            
            for i, worker in enumerate(workers, 1):
                print(f"  {i}. Worker: {worker['worker_name']} ({worker['worker_id'][:8]}...)")
                print(f"     分配ID: {worker['assignment_id'][:8]}...")
                print(f"     分配状态: {worker['assignment_status']}")
                print(f"     是否主要Worker: {'是' if worker.get('is_primary_worker') else '否'}")
            
            return workers
        else:
            print(f"❌ 正向查询失败: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 正向查询异常: {e}")
        return []

def test_reverse_query(worker_id, worker_name):
    """测试反向查询：Worker → 任务"""
    
    print(f"\n🔍 测试反向查询（Worker → 任务）...")
    print(f"Worker: {worker_name} ({worker_id[:8]}...)")
    
    try:
        response = requests.get(f"{BASE_URL}/crawler-workers/{worker_id}/assignments")
        if response.status_code == 200:
            assignments = response.json()
            print(f"✅ Worker '{worker_name}' 的任务分配数量: {len(assignments)}")
            
            for i, assignment in enumerate(assignments, 1):
                print(f"  {i}. 分配: {assignment['assignment_name']}")
                print(f"     分配ID: {assignment['assignment_id'][:8]}...")
                print(f"     任务ID: {assignment['task_id']}")
                print(f"     分配状态: {assignment['assignment_status']}")
                print(f"     创建时间: {assignment['created_at']}")
                print(f"     是否主要Worker: {'是' if assignment.get('is_primary_worker') else '否'}")
                print(f"     Worker数量: {assignment['worker_count']}")
                print(f"     总URL数: {assignment['total_urls']}")
                print(f"     已处理URL数: {assignment['processed_urls']}")
                print(f"     进度: {assignment['progress_rate']:.1%}")
            
            return assignments
        else:
            print(f"❌ 反向查询失败: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 反向查询异常: {e}")
        return []

def test_data_consistency(forward_workers, reverse_assignments):
    """测试数据一致性"""
    
    print(f"\n🔍 测试数据一致性...")
    
    # 从正向查询中提取分配ID
    forward_assignment_ids = set()
    for worker in forward_workers:
        if 'assignment_id' in worker:
            forward_assignment_ids.add(worker['assignment_id'])
    
    # 从反向查询中提取分配ID
    reverse_assignment_ids = set()
    for assignment in reverse_assignments:
        reverse_assignment_ids.add(assignment['assignment_id'])
    
    # 检查一致性
    common_assignments = forward_assignment_ids & reverse_assignment_ids
    forward_only = forward_assignment_ids - reverse_assignment_ids
    reverse_only = reverse_assignment_ids - forward_assignment_ids
    
    print(f"✅ 正向查询分配ID: {len(forward_assignment_ids)}个")
    print(f"✅ 反向查询分配ID: {len(reverse_assignment_ids)}个")
    print(f"✅ 共同分配ID: {len(common_assignments)}个")
    
    if forward_only:
        print(f"⚠️  仅在正向查询中的分配: {len(forward_only)}个")
        for aid in forward_only:
            print(f"    - {aid[:8]}...")
    
    if reverse_only:
        print(f"⚠️  仅在反向查询中的分配: {len(reverse_only)}个")
        for aid in reverse_only:
            print(f"    - {aid[:8]}...")
    
    # 一致性评分
    if len(forward_assignment_ids) == 0 and len(reverse_assignment_ids) == 0:
        consistency_score = 1.0
    elif len(forward_assignment_ids) == 0 or len(reverse_assignment_ids) == 0:
        consistency_score = 0.0
    else:
        consistency_score = len(common_assignments) / max(len(forward_assignment_ids), len(reverse_assignment_ids))
    
    print(f"📊 数据一致性评分: {consistency_score:.1%}")
    
    return consistency_score

def test_multiple_workers():
    """测试多个Worker的反向查询"""
    
    print(f"\n🔍 测试多个Worker的反向查询...")
    
    # 获取所有Worker
    try:
        response = requests.get(f"{BASE_URL}/crawler-workers/")
        if response.status_code == 200:
            workers = response.json()
            print(f"✅ 系统中总Worker数量: {len(workers)}")
            
            assignment_counts = {}
            total_assignments = 0
            
            for worker in workers[:3]:  # 测试前3个Worker
                worker_id = worker['worker_id']
                worker_name = worker['worker_name']
                
                # 获取该Worker的分配
                assign_response = requests.get(f"{BASE_URL}/crawler-workers/{worker_id}/assignments")
                if assign_response.status_code == 200:
                    assignments = assign_response.json()
                    assignment_counts[worker_name] = len(assignments)
                    total_assignments += len(assignments)
                    print(f"  - {worker_name}: {len(assignments)}个分配")
                else:
                    assignment_counts[worker_name] = -1
                    print(f"  - {worker_name}: 查询失败")
            
            print(f"📊 总分配数量: {total_assignments}")
            return assignment_counts
        else:
            print(f"❌ 获取Worker列表失败: {response.text}")
            return {}
            
    except Exception as e:
        print(f"❌ 多Worker测试异常: {e}")
        return {}

def main():
    """主函数"""
    print("🚀 开始测试Worker任务分配修复效果\n")
    print("=" * 80)
    
    # 1. 测试正向查询
    forward_workers = test_forward_query()
    
    print("=" * 80)
    
    # 2. 测试反向查询（使用正向查询中的第一个Worker）
    reverse_assignments = []
    if forward_workers:
        test_worker = forward_workers[0]
        worker_id = test_worker['worker_id']
        worker_name = test_worker['worker_name']
        reverse_assignments = test_reverse_query(worker_id, worker_name)
    else:
        # 如果正向查询没有结果，使用已知的Worker ID
        worker_id = "46ac2f77-1e6a-401c-8853-e22f03ccaa94"
        worker_name = "11237"
        reverse_assignments = test_reverse_query(worker_id, worker_name)
    
    print("=" * 80)
    
    # 3. 测试数据一致性
    consistency_score = test_data_consistency(forward_workers, reverse_assignments)
    
    print("=" * 80)
    
    # 4. 测试多个Worker
    worker_assignment_counts = test_multiple_workers()
    
    print("=" * 80)
    
    # 5. 总结测试结果
    print(f"\n🎯 测试结果总结:")
    print(f"  正向查询（任务→Worker）: {'✅ 正常' if forward_workers else '❌ 异常'}")
    print(f"  反向查询（Worker→任务）: {'✅ 正常' if reverse_assignments else '❌ 异常'}")
    print(f"  数据一致性: {'✅ 良好' if consistency_score >= 0.8 else '⚠️ 需要关注' if consistency_score >= 0.5 else '❌ 有问题'} ({consistency_score:.1%})")
    print(f"  多Worker测试: {'✅ 正常' if worker_assignment_counts else '❌ 异常'}")
    
    # 6. 修复效果评估
    if forward_workers and reverse_assignments and consistency_score >= 0.8:
        print(f"\n🎉 修复效果评估: ✅ 成功")
        print(f"  ✅ Worker任务分配API已修复")
        print(f"  ✅ 正向和反向查询都正常工作")
        print(f"  ✅ 数据一致性良好")
        print(f"  ✅ 前端页面应该能正确显示Worker的任务分配")
    else:
        print(f"\n❌ 修复效果评估: 仍有问题")
        if not forward_workers:
            print(f"  ❌ 正向查询异常")
        if not reverse_assignments:
            print(f"  ❌ 反向查询异常")
        if consistency_score < 0.8:
            print(f"  ❌ 数据一致性问题")
    
    print(f"\n📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
