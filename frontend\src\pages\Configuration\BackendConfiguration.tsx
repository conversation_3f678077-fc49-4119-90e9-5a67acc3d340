/**
 * 后端配置管理页面
 * 专门管理后端配置的独立页面
 */

import React, { useState, useEffect } from 'react';
import {
  Typography,
  Card,
  Button,
  Space,
  Table,
  Tag,
  Modal,
  message,
  Row,
  Col,
  Tooltip,
  Popconfirm,
  Statistic,
  Progress,
  Spin
} from 'antd';
import {
  CloudServerOutlined,
  PlayCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  CheckCircleOutlined,
  ReloadOutlined,
  EyeOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import type { TableColumnsType } from 'antd';

// 导入新架构的API和类型
import {
  backendConfigApi,
  BackendConfigSummary,
  BackendStatus,
  PerformanceLevel
} from '../../services';

// 导入子组件
import BackendConfigForm from './components/BackendConfigForm';

const { Title, Text } = Typography;

const BackendConfiguration: React.FC = () => {
  // 状态管理
  const [backendConfigs, setBackendConfigs] = useState<BackendConfigSummary[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedBackendConfig, setSelectedBackendConfig] = useState<string | null>(null);
  const [configDetailVisible, setConfigDetailVisible] = useState(false);
  const [backendConfigModalVisible, setBackendConfigModalVisible] = useState(false);
  const [editingBackendConfig, setEditingBackendConfig] = useState<string | null>(null);
  const [configDetailData, setConfigDetailData] = useState<any>(null);

  // 初始化数据
  useEffect(() => {
    fetchConfigs();
  }, []);

  // 获取配置数据
  const fetchConfigs = async () => {
    setLoading(true);
    try {
      const backendConfigsData = await backendConfigApi.getConfigs();
      setBackendConfigs(backendConfigsData);
    } catch (error) {
      message.error(`获取后端配置数据失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 后端配置相关操作
  const handleViewBackendConfig = async (configId: string) => {
    try {
      setLoading(true);
      const configData = await backendConfigApi.getConfig(configId);
      setConfigDetailData(configData);
      setSelectedBackendConfig(configId);
      setConfigDetailVisible(true);
    } catch (error) {
      message.error(`获取配置详情失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBackendConfig = () => {
    setEditingBackendConfig(null);
    setBackendConfigModalVisible(true);
  };

  const handleEditBackendConfig = (configId: string) => {
    setEditingBackendConfig(configId);
    setBackendConfigModalVisible(true);
  };

  const handleDeleteBackendConfig = async (configId: string) => {
    try {
      await backendConfigApi.deleteConfig(configId);
      message.success('删除后端配置成功');
      fetchConfigs();
    } catch (error) {
      message.error(`删除后端配置失败: ${error}`);
    }
  };

  const handleTestBackendConnection = async (configId: string) => {
    try {
      const config = await backendConfigApi.getConfig(configId);
      const result = await backendConfigApi.testConnection({
        backend_name: config.backend_name,
        api_endpoint: config.api_endpoint,
        timeout: config.timeout,
        max_retries: config.max_retries,
        auth_config: config.auth_config
      });
      
      if (result.success) {
        message.success(`连接测试成功，响应时间: ${result.response_time}ms`);
      } else {
        message.error(`连接测试失败: ${result.error_message}`);
      }
    } catch (error) {
      message.error(`连接测试失败: ${error}`);
    }
  };

  const handlePerformHealthCheck = async (configId: string) => {
    try {
      const result = await backendConfigApi.performHealthCheck(configId);
      if (result.is_healthy) {
        message.success(`健康检查通过，响应时间: ${result.response_time}ms`);
      } else {
        message.warning(`健康检查失败: ${result.error_message}`);
      }
      fetchConfigs(); // 刷新数据以显示最新的健康状态
    } catch (error) {
      message.error(`健康检查失败: ${error}`);
    }
  };

  // 状态标签渲染
  const renderBackendStatus = (status: BackendStatus) => {
    const statusConfig = {
      active: { color: 'green', text: '活跃' },
      inactive: { color: 'default', text: '未激活' },
      error: { color: 'red', text: '错误' },
      maintenance: { color: 'orange', text: '维护中' }
    };
    
    const config = statusConfig[status] || statusConfig.inactive;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const renderPerformanceLevel = (level: PerformanceLevel) => {
    const levelConfig = {
      low: { color: 'default', text: '低' },
      medium: { color: 'blue', text: '中' },
      high: { color: 'green', text: '高' },
      ultra: { color: 'purple', text: '超高' }
    };
    
    const config = levelConfig[level] || levelConfig.low;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 后端配置表格列定义
  const backendConfigColumns: TableColumnsType<BackendConfigSummary> = [
    {
      title: '配置名称',
      dataIndex: 'backend_name',
      key: 'backend_name',
      render: (text) => <Text strong>{text}</Text>,
    },
    {
      title: 'API端点',
      dataIndex: 'api_endpoint',
      key: 'api_endpoint',
      render: (url) => (
        <Tooltip title={url}>
          <Text code style={{ maxWidth: 200 }} ellipsis>{url}</Text>
        </Tooltip>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: renderBackendStatus,
    },
    {
      title: '性能等级',
      dataIndex: 'performance_level',
      key: 'performance_level',
      render: renderPerformanceLevel,
    },
    {
      title: '健康评分',
      dataIndex: 'health_score',
      key: 'health_score',
      render: (score) => (
        <Progress
          percent={score * 100}
          size="small"
          status={score >= 0.8 ? 'success' : score >= 0.5 ? 'normal' : 'exception'}
          format={() => `${(score * 100).toFixed(0)}%`}
        />
      ),
    },
    {
      title: '负载',
      key: 'load',
      render: (_, record) => (
        <Progress
          percent={(record.current_load / record.max_concurrent) * 100}
          size="small"
          format={() => `${record.current_load}/${record.max_concurrent}`}
        />
      ),
    },
    {
      title: '成功率',
      dataIndex: 'success_rate',
      key: 'success_rate',
      render: (rate) => `${(rate * 100).toFixed(1)}%`,
    },
    {
      title: '平均响应时间',
      dataIndex: 'avg_response_time',
      key: 'avg_response_time',
      render: (time) => `${time.toFixed(0)}ms`,
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewBackendConfig(record.backend_id)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => handleEditBackendConfig(record.backend_id)}
            />
          </Tooltip>
          <Tooltip title="测试连接">
            <Button 
              type="text" 
              icon={<ThunderboltOutlined />} 
              onClick={() => handleTestBackendConnection(record.backend_id)}
            />
          </Tooltip>
          <Tooltip title="健康检查">
            <Button 
              type="text" 
              icon={<CheckCircleOutlined />} 
              onClick={() => handlePerformHealthCheck(record.backend_id)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个配置吗？"
            onConfirm={() => handleDeleteBackendConfig(record.backend_id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <CloudServerOutlined /> 后端配置管理
        </Title>
        <Text type="secondary">
          管理后端配置，支持创建、编辑、测试连接和监控配置状态
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="后端配置总数"
              value={backendConfigs.length}
              prefix={<CloudServerOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃配置"
              value={backendConfigs.filter(c => c.status === 'active').length}
              prefix={<PlayCircleOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="健康后端"
              value={backendConfigs.filter(c => c.health_score >= 0.8).length}
              prefix={<CheckCircleOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均健康评分"
              value={backendConfigs.length > 0 ?
                (backendConfigs.reduce((sum, c) => sum + c.health_score, 0) / backendConfigs.length * 100).toFixed(1) : 0}
              prefix={<ThunderboltOutlined />}
              suffix="%"
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4} style={{ margin: 0 }}>配置列表</Title>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchConfigs}
              loading={loading}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateBackendConfig}
            >
              新建后端配置
            </Button>
          </Space>
        </div>

        <Spin spinning={loading}>
          <Table
            columns={backendConfigColumns}
            dataSource={backendConfigs}
            rowKey="backend_id"
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
              pageSizeOptions: ['10', '20', '50', '100']
            }}
          />
        </Spin>
      </Card>

      {/* 后端配置表单模态框 */}
      <Modal
        title={editingBackendConfig ? "编辑后端配置" : "新建后端配置"}
        open={backendConfigModalVisible}
        onCancel={() => setBackendConfigModalVisible(false)}
        footer={null}
        width={600}
        destroyOnHidden
      >
        <BackendConfigForm
          configId={editingBackendConfig}
          onSuccess={() => {
            setBackendConfigModalVisible(false);
            fetchConfigs();
          }}
          onCancel={() => setBackendConfigModalVisible(false)}
        />
      </Modal>

      {/* 配置详情模态框 */}
      <Modal
        title={`配置详情 - ${configDetailData?.backend_name || '未知配置'}`}
        open={configDetailVisible}
        onCancel={() => setConfigDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setConfigDetailVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {configDetailData && (
          <div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <strong>配置ID:</strong> {configDetailData.backend_id}
              </Col>
              <Col span={12}>
                <strong>配置名称:</strong> {configDetailData.backend_name}
              </Col>
              <Col span={24}>
                <strong>描述:</strong> {configDetailData.description || '无描述'}
              </Col>
              <Col span={12}>
                <strong>API端点:</strong> <Text code>{configDetailData.api_endpoint}</Text>
              </Col>
              <Col span={12}>
                <strong>状态:</strong> <Tag color={configDetailData.status === 'active' ? 'green' : 'default'}>{configDetailData.status}</Tag>
              </Col>
              <Col span={12}>
                <strong>性能等级:</strong> <Tag>{configDetailData.performance_level}</Tag>
              </Col>
              <Col span={12}>
                <strong>健康评分:</strong> {(configDetailData.health_score * 100).toFixed(1)}%
              </Col>
              <Col span={12}>
                <strong>最大并发:</strong> {configDetailData.max_concurrent}
              </Col>
              <Col span={12}>
                <strong>当前负载:</strong> {configDetailData.current_load}
              </Col>
              <Col span={12}>
                <strong>超时时间:</strong> {configDetailData.timeout}ms
              </Col>
              <Col span={12}>
                <strong>最大重试:</strong> {configDetailData.max_retries}
              </Col>
              <Col span={12}>
                <strong>创建时间:</strong> {configDetailData.created_at ? new Date(configDetailData.created_at).toLocaleString() : '-'}
              </Col>
              <Col span={12}>
                <strong>更新时间:</strong> {configDetailData.updated_at ? new Date(configDetailData.updated_at).toLocaleString() : '-'}
              </Col>
            </Row>

            <div style={{ marginTop: '24px' }}>
              <h4>完整配置</h4>
              <pre style={{
                background: '#f5f5f5',
                padding: '12px',
                borderRadius: '4px',
                fontSize: '12px',
                maxHeight: '300px',
                overflow: 'auto'
              }}>
                {JSON.stringify(configDetailData, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default BackendConfiguration;
