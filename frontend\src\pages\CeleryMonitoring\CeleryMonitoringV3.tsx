import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Tabs,
  Row,
  Col,
  Button,
  Space,
  Typography,
  Alert,
  Spin,
  Badge,
  Tooltip,
  Progress,
  Statistic,
  List,
  Tag,
  Timeline
} from 'antd';
import {
  ReloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  BarsOutlined,
  ExclamationCircleOutlined,
  TrophyOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

interface WorkerStatus {
  name: string;
  status: string;
  active_tasks: number;
  processed_tasks: number;
  failed_tasks: number;
  load_avg: number[];
  memory_usage: string;
  uptime: string;
  last_heartbeat: string;
}

interface LiveTask {
  id: string;
  name: string;
  worker: string;
  started_at: string;
  estimated_duration: string;
  progress: number;
  args: any[];
}

interface SystemIssue {
  type: string;
  category: string;
  message: string;
  suggestion: string;
}

const CeleryMonitoringV3: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [workers, setWorkers] = useState<WorkerStatus[]>([]);
  const [liveTasks, setLiveTasks] = useState<LiveTask[]>([]);
  const [diagnostics, setDiagnostics] = useState<{
    issues: SystemIssue[];
    queue_lengths: Record<string, number>;
  }>({ issues: [], queue_lengths: {} });
  const [autoRefresh, setAutoRefresh] = useState(true);

  // 获取Worker详细状态
  const fetchWorkerStatus = async () => {
    try {
      const response = await fetch('/api/v1/celery/workers/detailed');
      if (response.ok) {
        const data = await response.json();
        setWorkers(data);
      }
    } catch (error) {
      console.error('Failed to fetch worker status:', error);
    }
  };

  // 获取实时任务流
  const fetchLiveTasks = async () => {
    try {
      const response = await fetch('/api/v1/celery/tasks/live-stream');
      if (response.ok) {
        const data = await response.json();
        setLiveTasks(data.executing_tasks || []);
      }
    } catch (error) {
      console.error('Failed to fetch live tasks:', error);
    }
  };

  // 获取系统诊断
  const fetchDiagnostics = async () => {
    try {
      const response = await fetch('/api/v1/celery/health/diagnostics');
      if (response.ok) {
        const data = await response.json();
        setDiagnostics(data);
      }
    } catch (error) {
      console.error('Failed to fetch diagnostics:', error);
    }
  };

  // 刷新所有数据
  const handleRefresh = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchWorkerStatus(),
        fetchLiveTasks(),
        fetchDiagnostics()
      ]);
    } catch (error) {
      console.error('Failed to refresh data:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化和自动刷新
  useEffect(() => {
    handleRefresh();
  }, []);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(handleRefresh, 5000); // 每5秒刷新
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  // 计算系统健康度
  const calculateSystemHealth = () => {
    const onlineWorkers = workers.filter(w => w.status === 'online').length;
    const totalWorkers = workers.length || 1;
    const issueCount = diagnostics.issues.length;
    
    let health = (onlineWorkers / totalWorkers) * 100;
    health -= issueCount * 10; // 每个问题扣10分
    
    return Math.max(0, Math.min(100, health));
  };

  const systemHealth = calculateSystemHealth();

  // 系统概览面板
  const OverviewPanel = () => (
    <div>
      {/* 系统健康度 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card>
            <Row align="middle">
              <Col span={18}>
                <Title level={4} style={{ margin: 0 }}>系统健康度</Title>
                <Progress
                  percent={systemHealth}
                  status={systemHealth >= 80 ? 'success' : systemHealth >= 60 ? 'normal' : 'exception'}
                  strokeColor={systemHealth >= 80 ? '#52c41a' : systemHealth >= 60 ? '#1890ff' : '#ff4d4f'}
                />
              </Col>
              <Col span={6} style={{ textAlign: 'right' }}>
                <Statistic
                  value={systemHealth}
                  suffix="%"
                  valueStyle={{ color: systemHealth >= 80 ? '#52c41a' : systemHealth >= 60 ? '#1890ff' : '#ff4d4f' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 关键指标 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="在线Worker"
              value={workers.filter(w => w.status === 'online').length}
              suffix={`/ ${workers.length}`}
              prefix={<TeamOutlined />}
              valueStyle={{ color: workers.every(w => w.status === 'online') ? '#52c41a' : '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="执行中任务"
              value={liveTasks.length}
              prefix={<BarsOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="队列积压"
              value={Object.values(diagnostics.queue_lengths).reduce((a, b) => a + b, 0)}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: Object.values(diagnostics.queue_lengths).some(v => v > 10) ? '#ff4d4f' : '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="系统问题"
              value={diagnostics.issues.length}
              prefix={<WarningOutlined />}
              valueStyle={{ color: diagnostics.issues.length > 0 ? '#ff4d4f' : '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 系统问题警告 */}
      {diagnostics.issues.length > 0 && (
        <Alert
          message="发现系统问题"
          description={
            <List
              size="small"
              dataSource={diagnostics.issues}
              renderItem={(issue) => (
                <List.Item>
                  <Space>
                    <Tag color={issue.type === 'error' ? 'red' : 'orange'}>
                      {issue.type.toUpperCase()}
                    </Tag>
                    <Text>{issue.message}</Text>
                    <Text type="secondary">建议: {issue.suggestion}</Text>
                  </Space>
                </List.Item>
              )}
            />
          }
          type={diagnostics.issues.some(i => i.type === 'error') ? 'error' : 'warning'}
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}
    </div>
  );

  // Worker状态面板
  const WorkerPanel = () => (
    <div>
      <Row gutter={16}>
        {workers.map((worker, index) => (
          <Col span={12} key={index} style={{ marginBottom: 16 }}>
            <Card
              title={
                <Space>
                  <TeamOutlined />
                  <Text strong>{worker.name}</Text>
                  <Tag color={worker.status === 'online' ? 'green' : 'red'}>
                    {worker.status}
                  </Tag>
                </Space>
              }
            >
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="活跃任务"
                    value={worker.active_tasks}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="已处理"
                    value={worker.processed_tasks}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="内存使用"
                    value={worker.memory_usage}
                    valueStyle={{ color: '#faad14' }}
                  />
                </Col>
              </Row>
              <div style={{ marginTop: 16 }}>
                <Text type="secondary">运行时间: {worker.uptime}</Text>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );

  // 实时任务面板
  const LiveTaskPanel = () => (
    <div>
      {liveTasks.length === 0 ? (
        <Card>
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <CheckCircleOutlined style={{ fontSize: 48, color: '#52c41a' }} />
            <Title level={4}>当前没有执行中的任务</Title>
            <Text type="secondary">系统空闲中</Text>
          </div>
        </Card>
      ) : (
        <List
          dataSource={liveTasks}
          renderItem={(task) => (
            <List.Item>
              <Card style={{ width: '100%' }}>
                <Row justify="space-between" align="middle">
                  <Col span={16}>
                    <Space direction="vertical" size="small">
                      <Text strong>{task.name}</Text>
                      <Text type="secondary">Worker: {task.worker}</Text>
                      <Text type="secondary">任务ID: {task.id.slice(0, 8)}...</Text>
                    </Space>
                  </Col>
                  <Col span={8} style={{ textAlign: 'right' }}>
                    <Space direction="vertical" size="small">
                      <Progress
                        percent={task.progress}
                        size="small"
                        status={task.progress >= 95 ? 'success' : 'active'}
                      />
                      <Text type="secondary">预计剩余: {task.estimated_duration}</Text>
                    </Space>
                  </Col>
                </Row>
              </Card>
            </List.Item>
          )}
        />
      )}
    </div>
  );

  const tabItems = [
    {
      key: 'overview',
      label: (
        <Space>
          <TrophyOutlined />
          系统概览
          {diagnostics.issues.length > 0 && (
            <Badge count={diagnostics.issues.length} size="small" />
          )}
        </Space>
      ),
      children: <OverviewPanel />
    },
    {
      key: 'workers',
      label: (
        <Space>
          <TeamOutlined />
          Worker状态
          <Badge count={workers.filter(w => w.status === 'online').length} size="small" style={{ backgroundColor: '#52c41a' }} />
        </Space>
      ),
      children: <WorkerPanel />
    },
    {
      key: 'tasks',
      label: (
        <Space>
          <BarsOutlined />
          实时任务
          {liveTasks.length > 0 && (
            <Badge count={liveTasks.length} size="small" style={{ backgroundColor: '#1890ff' }} />
          )}
        </Space>
      ),
      children: <LiveTaskPanel />
    }
  ];

  return (
    <div style={{ padding: 24, background: '#f5f5f5', minHeight: '100vh' }}>
      {/* 页面头部 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Col>
          <Title level={2} style={{ margin: 0 }}>
            Celery监控与管理中心
          </Title>
          <Text type="secondary">
            专注于Celery服务状态监控和任务管理
          </Text>
        </Col>
        <Col>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading}
              type="primary"
            >
              刷新
            </Button>
            <Tooltip title={autoRefresh ? '点击关闭自动刷新' : '点击开启自动刷新'}>
              <Button
                icon={autoRefresh ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={() => setAutoRefresh(!autoRefresh)}
                type={autoRefresh ? 'default' : 'dashed'}
              >
                {autoRefresh ? '自动刷新' : '手动模式'}
              </Button>
            </Tooltip>
          </Space>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
        />
      </Card>
    </div>
  );
};

export default CeleryMonitoringV3;
