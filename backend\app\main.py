from fastapi import FastAPI, Request, Depends, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from contextlib import asynccontextmanager
import sys
import os
import logging
import json
from datetime import datetime
from typing import List

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from config.settings import settings
from app.database import startup_database, shutdown_database, get_database_info, check_database_connection
from app.schemas.common import HealthCheckResponse

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("应用启动中...")
    try:
        await startup_database()

        # 启动任务管理器
        try:
            from app.api.task_routes import get_task_manager
            task_manager = await get_task_manager()
            logger.info("任务管理器启动成功")
        except Exception as e:
            logger.warning(f"任务管理器启动失败: {e}")
            # 不阻止应用启动，任务管理器可以稍后手动启动

        logger.info("应用启动完成")
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        raise

    yield

    # 关闭时执行
    logger.info("应用关闭中...")

    # 停止任务管理器
    try:
        from app.api.task_routes import _task_manager
        if _task_manager:
            await _task_manager.stop()
            logger.info("任务管理器已停止")
    except Exception as e:
        logger.warning(f"停止任务管理器时出错: {e}")

    await shutdown_database()
    logger.info("应用关闭完成")


app = FastAPI(
    title=settings.APP_NAME,
    version=settings.VERSION,
    debug=settings.DEBUG,
    openapi_url=f"{settings.API_PREFIX}/openapi.json" if settings.DEBUG else None,
    lifespan=lifespan,
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic验证错误处理
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """处理Pydantic验证错误，确保返回字符串格式的错误信息"""
    error_messages = []
    for error in exc.errors():
        field_path = " -> ".join(str(loc) for loc in error["loc"])
        error_type = error["type"]
        message = error["msg"]

        # 针对常见错误类型提供更友好的错误信息
        if error_type == "missing":
            if "name" in field_path:
                error_messages.append("任务名称不能为空")
            elif "platform" in field_path:
                error_messages.append("请选择目标平台")
            elif "url_ids" in field_path:
                error_messages.append("必须选择至少一个URL")
            else:
                error_messages.append(f"{field_path}: 此字段为必填项")
        elif error_type == "string_too_short":
            if "name" in field_path:
                error_messages.append("任务名称不能为空")
            else:
                error_messages.append(f"{field_path}: {message}")
        elif error_type == "string_too_long":
            if "name" in field_path:
                error_messages.append("任务名称不能超过100个字符")
            else:
                error_messages.append(f"{field_path}: {message}")
        else:
            error_messages.append(f"{field_path}: {message}")

    return JSONResponse(
        status_code=422,
        content={
            "detail": "请求参数验证失败",
            "errors": error_messages,
            "error_type": "ValidationError"
        }
    )

# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=500,
        content={
            "detail": f"Internal server error: {str(exc)}" if settings.DEBUG else "Internal server error",
            "error_type": type(exc).__name__
        }
    )

# 健康检查端点
@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """健康检查端点"""
    from datetime import datetime
    
    # 检查数据库连接
    db_connected = await check_database_connection()
    db_info = await get_database_info() if db_connected else {}
    
    if db_connected:
        return HealthCheckResponse.create_healthy(
            app_name=settings.APP_NAME,
            version=settings.VERSION,
            environment=settings.ENVIRONMENT,
            database_status="connected",
            **db_info
        )
    else:
        return HealthCheckResponse.create_unhealthy(
            app_name=settings.APP_NAME,
            version=settings.VERSION,
            environment=settings.ENVIRONMENT,
            database_status="disconnected"
        )

# 根路径
@app.get("/")
async def root():
    return {
        "message": f"Welcome to {settings.APP_NAME}",
        "version": settings.VERSION,
        "docs_url": "/docs" if settings.DEBUG else None
    }

# 系统状态端点
@app.get("/system/status")
async def get_system_status():
    """获取系统状态"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "database": "connected",
            "redis": "connected",
            "websocket": "active"
        },
        "websocket_connections": len(manager.active_connections)
    }

# 基本任务端点
@app.get("/tasks")
async def get_tasks(page: int = 1, page_size: int = 100):
    """获取任务列表"""
    return {
        "success": True,
        "data": [],
        "pagination": {
            "page": page,
            "page_size": page_size,
            "total": 0,
            "total_pages": 0
        }
    }

# WebSocket状态端点
@app.get("/ws/status")
async def get_websocket_status():
    """获取WebSocket连接状态"""
    return {
        "active_connections": len(manager.active_connections),
        "status": "active" if len(manager.active_connections) > 0 else "inactive",
        "timestamp": datetime.now().isoformat()
    }

# 测试WebSocket广播端点
@app.post("/ws/broadcast")
async def broadcast_test_message(message: str = "Test broadcast message"):
    """广播测试消息到所有WebSocket连接"""
    test_message = {
        "type": "test_broadcast",
        "data": {"message": message},
        "timestamp": datetime.now().isoformat()
    }
    await manager.broadcast(json.dumps(test_message))
    return {
        "success": True,
        "message": f"Broadcasted to {len(manager.active_connections)} connections",
        "connections": len(manager.active_connections)
    }

# 增强的WebSocket连接管理器
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}  # {connection_id: websocket}
        self.task_subscriptions: Dict[str, Set[str]] = {}  # {task_id: set(connection_ids)}
        self.worker_subscriptions: Dict[str, Set[str]] = {}  # {worker_id: set(connection_ids)}
        self.global_subscriptions: Set[str] = set()  # connection_ids

    async def connect(self, websocket: WebSocket, connection_id: str = None):
        await websocket.accept()
        if connection_id is None:
            connection_id = f"conn_{len(self.active_connections)}_{datetime.now().timestamp()}"

        self.active_connections[connection_id] = websocket
        logger.info(f"WebSocket连接已建立: {connection_id}，当前连接数: {len(self.active_connections)}")
        return connection_id

    def disconnect(self, connection_id: str):
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]

        # 清理订阅
        self.global_subscriptions.discard(connection_id)
        for task_id in list(self.task_subscriptions.keys()):
            self.task_subscriptions[task_id].discard(connection_id)
            if not self.task_subscriptions[task_id]:
                del self.task_subscriptions[task_id]

        for worker_id in list(self.worker_subscriptions.keys()):
            self.worker_subscriptions[worker_id].discard(connection_id)
            if not self.worker_subscriptions[worker_id]:
                del self.worker_subscriptions[worker_id]

        logger.info(f"WebSocket连接已断开: {connection_id}，当前连接数: {len(self.active_connections)}")

    def subscribe_to_task(self, connection_id: str, task_id: str):
        """订阅任务事件"""
        if task_id not in self.task_subscriptions:
            self.task_subscriptions[task_id] = set()
        self.task_subscriptions[task_id].add(connection_id)

    def subscribe_to_worker(self, connection_id: str, worker_id: str):
        """订阅Worker事件"""
        if worker_id not in self.worker_subscriptions:
            self.worker_subscriptions[worker_id] = set()
        self.worker_subscriptions[worker_id].add(connection_id)

    def subscribe_global(self, connection_id: str):
        """订阅全局事件"""
        self.global_subscriptions.add(connection_id)

    async def send_personal_message(self, message: str, connection_id: str):
        if connection_id in self.active_connections:
            try:
                websocket = self.active_connections[connection_id]
                await websocket.send_text(message)
            except Exception as e:
                logger.error(f"发送个人消息失败: {e}")
                self.disconnect(connection_id)

    async def broadcast(self, message: str):
        disconnected = []
        for connection_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                disconnected.append(connection_id)

        # 清理断开的连接
        for connection_id in disconnected:
            self.disconnect(connection_id)

    async def broadcast_to_task(self, task_id: str, message: str):
        """广播消息到任务订阅者"""
        if task_id in self.task_subscriptions:
            disconnected = []
            for connection_id in self.task_subscriptions[task_id].copy():
                if connection_id in self.active_connections:
                    try:
                        websocket = self.active_connections[connection_id]
                        await websocket.send_text(message)
                    except Exception as e:
                        logger.error(f"发送任务消息失败: {e}")
                        disconnected.append(connection_id)

            for connection_id in disconnected:
                self.disconnect(connection_id)

    async def broadcast_to_worker(self, worker_id: str, message: str):
        """广播消息到Worker订阅者"""
        if worker_id in self.worker_subscriptions:
            disconnected = []
            for connection_id in self.worker_subscriptions[worker_id].copy():
                if connection_id in self.active_connections:
                    try:
                        websocket = self.active_connections[connection_id]
                        await websocket.send_text(message)
                    except Exception as e:
                        logger.error(f"发送Worker消息失败: {e}")
                        disconnected.append(connection_id)

            for connection_id in disconnected:
                self.disconnect(connection_id)

# 创建连接管理器实例
manager = ConnectionManager()

# 增强的WebSocket端点
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket, connection_id: str = None):
    if connection_id is None:
        connection_id = f"conn_{datetime.now().timestamp()}"

    connection_id = await manager.connect(websocket, connection_id)
    try:
        # 发送欢迎消息
        welcome_message = {
            "type": "system_message",
            "data": {
                "message": "WebSocket连接已建立",
                "connection_id": connection_id,
                "timestamp": datetime.now().isoformat()
            },
            "timestamp": datetime.now().isoformat()
        }
        await manager.send_personal_message(json.dumps(welcome_message), connection_id)

        # 保持连接并处理消息
        while True:
            try:
                # 等待客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)
                action = message.get("action") or message.get("type")

                # 处理订阅消息
                if action == "subscribe_task":
                    task_id = message.get("task_id")
                    if task_id:
                        manager.subscribe_to_task(connection_id, task_id)
                        response = {
                            "type": "subscription_success",
                            "data": {"subscribed_to": f"task:{task_id}"},
                            "timestamp": datetime.now().isoformat()
                        }
                        await manager.send_personal_message(json.dumps(response), connection_id)

                elif action == "subscribe_worker":
                    worker_id = message.get("worker_id")
                    if worker_id:
                        manager.subscribe_to_worker(connection_id, worker_id)
                        response = {
                            "type": "subscription_success",
                            "data": {"subscribed_to": f"worker:{worker_id}"},
                            "timestamp": datetime.now().isoformat()
                        }
                        await manager.send_personal_message(json.dumps(response), connection_id)

                elif action == "subscribe_global":
                    manager.subscribe_global(connection_id)
                    response = {
                        "type": "subscription_success",
                        "data": {"subscribed_to": "global"},
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(response), connection_id)

                # 处理ping消息
                elif action == "ping":
                    pong_message = {
                        "type": "pong",
                        "data": {"message": "pong"},
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(pong_message), connection_id)

                # 回显其他消息
                else:
                    echo_message = {
                        "type": "echo",
                        "data": message,
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(echo_message), connection_id)

            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                error_message = {
                    "type": "error",
                    "data": {"message": "Invalid JSON format"},
                    "timestamp": datetime.now().isoformat()
                }
                await manager.send_personal_message(json.dumps(error_message), connection_id)
            except Exception as e:
                logger.error(f"WebSocket处理消息时出错: {e}")
                break

    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"WebSocket连接出错: {e}")
    finally:
        manager.disconnect(connection_id)

# API路由
try:
    from app.api.task_routes import router as task_router
    from app.api.monitoring_routes import router as monitoring_router
    from app.api.monitoring_task_routes import router as monitoring_task_router
    from app.api.excel_routes import router as excel_router
    from app.api.url_pool_routes import router as url_pool_router
    from app.api.task_create_routes import router as task_create_router
    from app.api.v1.crawler_config import router as crawler_config_router

    from app.api.v1.crawler_instance_config import router as crawler_instance_config_router

    # Celery监控路由
    from app.api.celery_monitoring_routes import router as celery_monitoring_router

    # 新架构API路由
    from app.api.v1.crawler_config_new import router as crawler_config_new_router
    from app.api.v1.backend_config import router as backend_config_router
    from app.api.v1.crawler_worker import router as crawler_worker_router
    from app.api.v1.task_assignment import router as task_assignment_router

    app.include_router(task_router)
    app.include_router(monitoring_router)
    app.include_router(monitoring_task_router)
    app.include_router(excel_router)
    app.include_router(url_pool_router)
    app.include_router(task_create_router)
    app.include_router(crawler_config_router, prefix="/api/v1/crawler", tags=["crawler"])

    app.include_router(crawler_instance_config_router, prefix="/api/v1", tags=["crawler-instance-config"])

    # Celery监控路由
    app.include_router(celery_monitoring_router, prefix="/api/v1", tags=["celery-monitoring"])

    # 爬虫调试路由
    try:
        from app.api.crawler_debug_routes import router as crawler_debug_router
        app.include_router(crawler_debug_router, tags=["crawler-debug"])
        logger.info("Successfully loaded crawler debug routes")
    except ImportError as e:
        logger.warning(f"Could not import crawler debug routes: {e}")

    # 新架构API路由
    app.include_router(crawler_config_new_router, prefix="/api/v1")
    app.include_router(backend_config_router, prefix="/api/v1")
    app.include_router(crawler_worker_router, prefix="/api/v1")
    app.include_router(task_assignment_router, prefix="/api/v1")

    logger.info("Successfully loaded all API routes (including new architecture)")
except ImportError as e:
    logger.warning(f"Could not import API routes: {e}")
    # 创建基本的任务路由作为备用
    from fastapi import APIRouter
    task_router = APIRouter(prefix="/api/v1/tasks", tags=["tasks"])

    @task_router.post("/submit")
    async def submit_task_fallback():
        return {"message": "Task submission API not implemented yet"}

    app.include_router(task_router)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    ) 