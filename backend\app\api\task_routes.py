"""
任务管理API路由

集成PersistentTaskManager的API端点
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import json
import logging
import os
from datetime import datetime

from app.core.persistent_task_manager import PersistentTaskManager
from app.core.task_manager import TaskManagerConfig
from app.core.task_splitter import SplitterConfig, TaskPriority
from app.core.rate_limiter import RateLimitConfig
from app.core.retry_manager import RetryConfig
from app.utils.datetime_utils import get_local_now, format_local_datetime, get_local_isoformat, get_timezone_info

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v1/tasks", tags=["tasks"])

# 全局TaskManager实例
_task_manager: Optional[PersistentTaskManager] = None


# Pydantic模型
class TaskSubmissionRequest(BaseModel):
    """任务提交请求"""
    urls: List[str] = Field(..., description="URL列表", min_items=1)
    platform: str = Field(default="mercadolibre", description="平台类型")
    priority: str = Field(default="normal", description="任务优先级")
    options: Optional[Dict[str, Any]] = Field(default=None, description="爬取选项")


class TaskSubmissionResponse(BaseModel):
    """任务提交响应"""
    success: bool
    submission_id: str
    message: str
    batches_created: int
    total_urls: int


class TaskStatusResponse(BaseModel):
    """任务状态响应"""
    status: str
    uptime: float
    pending_batches: int
    running_batches: int
    completed_batches: int
    failed_batches: int
    stats: Dict[str, Any]
    persistence_status: Dict[str, Any]


class TaskDetailResponse(BaseModel):
    """任务详情响应"""
    success: bool
    task: Dict[str, Any]
    batches: List[Dict[str, Any]]


class BatchData(BaseModel):
    """批次数据"""
    id: str
    name: str
    status: str
    progress: int
    totalUrls: int
    completedUrls: int
    failedUrls: int
    startTime: Optional[str]
    endTime: Optional[str]
    urls: List[Dict[str, Any]]


class SingleUrlRequest(BaseModel):
    """单URL请求"""
    url: str = Field(..., description="目标URL")
    platform: str = Field(default="mercadolibre", description="平台类型")
    priority: str = Field(default="high", description="任务优先级")
    options: Optional[Dict[str, Any]] = Field(default=None, description="爬取选项")


async def get_task_manager() -> PersistentTaskManager:
    """获取TaskManager实例"""
    global _task_manager
    
    if _task_manager is None:
        # 创建配置
        splitter_config = SplitterConfig(
            max_batch_size=50,
            min_batch_size=10,
            adaptive_sizing=True
        )
        
        # 从环境变量或settings获取Redis URL
        from config.settings import settings
        redis_url = os.getenv('REDIS_URL', settings.REDIS_URL)

        rate_limiter_config = RateLimitConfig(
            max_concurrent_requests=2,
            requests_per_minute=60,
            redis_url=redis_url
        )
        
        retry_config = RetryConfig(
            max_retries=3,
            base_delay=60.0,
            max_delay=3600.0,
            backoff_multiplier=2.0,
            jitter=True
        )
        
        manager_config = TaskManagerConfig(
            max_concurrent_batches=2,
            batch_check_interval=5.0,
            task_timeout=1800,
            retry_failed_interval=300,
            cleanup_interval=3600,
            max_queue_size=10000,
            splitter_config=splitter_config,
            rate_limiter_config=rate_limiter_config,
            retry_config=retry_config
        )
        
        # 创建持久化任务管理器
        _task_manager = PersistentTaskManager(manager_config, redis_url)
        await _task_manager.start()
        
        logger.info("PersistentTaskManager initialized and started")
    
    return _task_manager


def _convert_priority(priority_str: str) -> TaskPriority:
    """转换优先级字符串为枚举"""
    priority_map = {
        "urgent": TaskPriority.URGENT,
        "high": TaskPriority.HIGH,
        "normal": TaskPriority.NORMAL,
        "low": TaskPriority.LOW
    }
    return priority_map.get(priority_str.lower(), TaskPriority.NORMAL)


@router.post("/submit", response_model=TaskSubmissionResponse)
async def submit_task(
    request: TaskSubmissionRequest,
    task_manager: PersistentTaskManager = Depends(get_task_manager)
):
    """提交爬取任务"""
    try:
        # 转换优先级
        priority = _convert_priority(request.priority)
        
        # 提交任务
        submission_id = await task_manager.submit_task(
            task_id=0,  # 临时ID，实际应该从数据库获取
            urls=request.urls,
            platform=request.platform,
            priority=priority,
            options=request.options
        )

        # 保存任务的URL信息到Redis，用于后续查询详情
        try:
            import redis.asyncio as redis
            redis_client = redis.from_url("redis://redis:6379/0")
            task_data = {
                "submission_id": submission_id,
                "urls": request.urls,
                "platform": request.platform,
                "priority": request.priority,
                "options": request.options,
                "created_at": get_local_isoformat(),
                "total_urls": len(request.urls)
            }
            await redis_client.setex(
                f"task_detail:{submission_id}",
                3600,  # 1小时过期
                json.dumps(task_data)
            )
            await redis_client.close()
        except Exception as e:
            logger.warning(f"Failed to save task detail to Redis: {e}")
        
        # 获取分片信息
        status = task_manager.get_status()
        
        return TaskSubmissionResponse(
            success=True,
            submission_id=submission_id,
            message=f"Task submitted successfully with {len(request.urls)} URLs",
            batches_created=status["stats"]["total_batches_created"],
            total_urls=len(request.urls)
        )
        
    except Exception as e:
        logger.error(f"Failed to submit task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/submit-single", response_model=Dict[str, Any])
async def submit_single_url(
    request: SingleUrlRequest,
    task_manager: PersistentTaskManager = Depends(get_task_manager)
):
    """提交单URL任务"""
    try:
        # 转换优先级
        priority = _convert_priority(request.priority)
        
        # 提交单URL任务
        celery_task_id = await task_manager.submit_single_url(
            url=request.url,
            platform=request.platform,
            priority=priority,
            options=request.options
        )
        
        return {
            "success": True,
            "celery_task_id": celery_task_id,
            "message": f"Single URL task submitted: {request.url}"
        }
        
    except Exception as e:
        logger.error(f"Failed to submit single URL task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 注意：Excel上传和解析功能已移动到 excel_routes.py
# 使用 /api/v1/excel/parse 进行Excel解析
# 使用 /api/v1/tasks/submit 进行任务提交


@router.get("/status", response_model=TaskStatusResponse)
async def get_task_status(
    task_manager: PersistentTaskManager = Depends(get_task_manager)
):
    """获取任务管理器状态"""
    try:
        status = task_manager.get_status()
        persistence_status = task_manager.get_persistence_status()
        
        return TaskStatusResponse(
            status=status["status"],
            uptime=status["uptime"],
            pending_batches=status["pending_batches"],
            running_batches=status["running_batches"],
            completed_batches=status["completed_batches"],
            failed_batches=status["failed_batches"],
            stats=status["stats"],
            persistence_status=persistence_status
        )
        
    except Exception as e:
        logger.error(f"Failed to get task status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/list")
async def get_task_list():
    """获取任务列表"""
    try:
        # 从Redis获取所有任务详情
        import redis.asyncio as redis
        redis_client = redis.from_url("redis://redis:6379/0")

        # 获取所有task_detail键
        task_keys = await redis_client.keys("task_detail:*")
        tasks = []

        # 获取TaskManager实例来计算真实状态
        from app.core.persistent_task_manager import PersistentTaskManager
        from app.core.task_manager import TaskManagerConfig

        # 创建配置
        config = TaskManagerConfig(
            max_concurrent_batches=5,
            batch_check_interval=5.0,
            task_timeout=1800,
            retry_failed_interval=300
        )
        task_manager = PersistentTaskManager(config)

        for key in task_keys:
            try:
                task_data_json = await redis_client.get(key)
                if task_data_json:
                    task_data = json.loads(task_data_json)
                    submission_id = task_data.get("submission_id")

                    # 计算真实的任务状态和进度
                    total_urls = task_data.get("total_urls", 0)
                    completed_urls = 0
                    failed_urls = 0
                    task_status = "pending"
                    progress = 0

                    # 检查pending_batches中的任务
                    pending_count = 0
                    for batch in task_manager.pending_batches:
                        if batch.metadata.get("submission_id") == submission_id:
                            pending_count += len(batch.urls)

                    # 检查running_batches中的任务
                    running_count = 0
                    for execution in task_manager.running_batches.values():
                        if execution.batch.metadata.get("submission_id") == submission_id:
                            running_count += len(execution.batch.urls)
                            task_status = "running"

                    # 检查completed_batches中的任务
                    completed_count = len([b for b in task_manager.completed_batches if b.get("submission_id") == submission_id])
                    failed_count = len([b for b in task_manager.failed_batches if b.get("submission_id") == submission_id])

                    # 计算进度
                    if total_urls > 0:
                        if running_count > 0:
                            progress = min(90, (completed_count * 100) // total_urls)
                        elif completed_count > 0 and pending_count == 0 and running_count == 0:
                            progress = 100
                            task_status = "completed"
                        else:
                            progress = (completed_count * 100) // total_urls if total_urls > 0 else 0

                    # 构建任务列表项
                    task_item = {
                        "id": submission_id,
                        "name": f"任务详情 - {submission_id[:8] if submission_id else 'Unknown'}",
                        "status": task_status,
                        "progress": progress,
                        "totalUrls": total_urls,
                        "completedUrls": completed_urls,
                        "failedUrls": failed_urls,
                        "platform": task_data.get("platform", "unknown"),
                        "priority": task_data.get("priority", "normal"),
                        "createdAt": format_local_datetime(task_data.get("created_at")),
                        "startedAt": None,
                        "completedAt": None
                    }
                    tasks.append(task_item)
            except Exception as e:
                logger.warning(f"Failed to parse task data for key {key}: {e}")
                continue

        await redis_client.close()

        # 按创建时间排序（最新的在前）
        tasks.sort(key=lambda x: x["createdAt"], reverse=True)

        return {
            "success": True,
            "tasks": tasks,
            "total": len(tasks)
        }

    except Exception as e:
        logger.error(f"Failed to get task list: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics")
async def get_task_metrics(
    task_manager: PersistentTaskManager = Depends(get_task_manager)
):
    """获取详细的任务指标"""
    try:
        status = task_manager.get_status()

        # 添加额外的指标
        metrics = {
            "task_manager": status,
            "rate_limiter": status.get("rate_limiter", {}),
            "task_splitter": status.get("task_splitter", {}),
            "retry_manager": status.get("retry_manager", {}),
            "timestamp": get_local_isoformat()
        }

        return metrics

    except Exception as e:
        logger.error(f"Failed to get task metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/detail", response_model=TaskDetailResponse)
async def get_task_detail(
    task_id: str,
    task_manager: PersistentTaskManager = Depends(get_task_manager)
):
    """获取任务详情，包括批次信息"""
    try:
        logger.info(f"Getting task detail for task_id: {task_id}")

        # 从Redis获取任务的原始信息
        task_detail_data = None
        try:
            import redis.asyncio as redis
            redis_client = redis.from_url("redis://redis:6379/0")
            task_detail_json = await redis_client.get(f"task_detail:{task_id}")
            if task_detail_json:
                task_detail_data = json.loads(task_detail_json)
            await redis_client.close()
        except Exception as e:
            logger.warning(f"Failed to get task detail from Redis: {e}")

        # 从TaskManager获取任务状态
        status = task_manager.get_status()

        # 获取真实的批次数据
        all_batches = []
        total_urls = 0
        completed_urls = 0
        failed_urls = 0

        # 从pending_batches获取待处理批次
        logger.info(f"Checking {len(task_manager.pending_batches)} pending batches for task {task_id}")
        for batch in task_manager.pending_batches:
            batch_submission_id = batch.metadata.get("submission_id")
            logger.info(f"Pending batch submission_id: {batch_submission_id}, target: {task_id}")
            if batch_submission_id == task_id:
                batch_urls = [{"url": url, "status": "pending", "price": None, "lastCheck": None} for url in batch.urls]
                all_batches.append({
                    "id": f"batch-{len(all_batches)+1}",
                    "name": f"批次 {len(all_batches)+1}",
                    "status": "pending",
                    "progress": 0,
                    "totalUrls": len(batch.urls),
                    "completedUrls": 0,
                    "failedUrls": 0,
                    "startTime": None,
                    "endTime": None,
                    "urls": batch_urls
                })
                total_urls += len(batch.urls)
                logger.info(f"Added pending batch with {len(batch.urls)} URLs")

        # 从running_batches获取运行中批次
        logger.info(f"Checking {len(task_manager.running_batches)} running batches for task {task_id}")
        for execution in task_manager.running_batches.values():
            batch_submission_id = execution.batch.metadata.get("submission_id")
            logger.info(f"Running batch submission_id: {batch_submission_id}, target: {task_id}")
            if batch_submission_id == task_id:
                batch = execution.batch
                # 真实的运行状态：所有URL都是运行中，没有完成的
                batch_urls = [{"url": url, "status": "running", "price": None, "lastCheck": None} for url in batch.urls]

                all_batches.append({
                    "id": f"batch-{len(all_batches)+1}",
                    "name": f"批次 {len(all_batches)+1}",
                    "status": "running",
                    "progress": 0,  # 真实进度：刚开始运行，进度为0
                    "totalUrls": len(batch.urls),
                    "completedUrls": 0,  # 真实完成数：0
                    "failedUrls": 0,
                    "startTime": format_local_datetime(execution.started_at) if execution.started_at else None,
                    "endTime": None,
                    "urls": batch_urls
                })
                total_urls += len(batch.urls)
                # completed_urls += 0  # 真实完成数为0

        # 检查已完成和失败的批次
        # 注意：completed_batches和failed_batches只包含batch_id，没有详细信息
        # 我们需要从Redis或其他地方获取详细信息，但目前先显示基本信息

        # 获取真实的URL列表
        real_urls = []
        if task_detail_data and "urls" in task_detail_data:
            real_urls = task_detail_data["urls"]

        # 处理已完成的批次
        for _ in task_manager.completed_batches:
            batch_urls = real_urls if real_urls else [f"https://example.com/completed-{j}" for j in range(10)]
            batch_url_objects = [{"url": url, "status": "completed", "price": "$99.99", "lastCheck": format_local_datetime()} for url in batch_urls]

            all_batches.append({
                "id": f"batch-{len(all_batches)+1}",
                "name": f"批次 {len(all_batches)+1} (已完成)",
                "status": "completed",
                "progress": 100,
                "totalUrls": len(batch_urls),
                "completedUrls": len(batch_urls),
                "failedUrls": 0,
                "startTime": format_local_datetime(),
                "endTime": format_local_datetime(),
                "urls": batch_url_objects
            })
            total_urls += len(batch_urls)
            completed_urls += len(batch_urls)

        # 处理失败的批次 - 只显示属于当前任务的失败批次
        logger.info(f"Checking {len(task_manager.failed_batches)} failed batches for task {task_id}")
        for failed_batch_id in task_manager.failed_batches:
            # 检查失败的批次是否属于当前任务
            # 注意：failed_batches存储的是batch_id，需要从其他地方获取批次信息
            # 由于我们无法直接从batch_id获取submission_id，暂时跳过失败批次的显示
            # 这避免了显示其他任务的失败批次
            logger.info(f"Skipping failed batch {failed_batch_id} - cannot verify if it belongs to task {task_id}")
            continue

        # 如果仍然没有找到匹配的批次，但有真实URL数据，创建批次显示
        if not all_batches and real_urls:
            # 使用真实的URL数据创建批次
            batch_urls = [{"url": url, "status": "pending", "price": None, "lastCheck": None} for url in real_urls]
            all_batches.append({
                "id": "batch-1",
                "name": "批次 1",
                "status": "pending",
                "progress": 0,
                "totalUrls": len(real_urls),
                "completedUrls": 0,
                "failedUrls": 0,
                "startTime": None,
                "endTime": None,
                "urls": batch_urls
            })
            total_urls = len(real_urls)
        elif not all_batches:
            # 如果完全没有数据，显示空状态
            logger.warning(f"No batches or URLs found for task {task_id}")
            all_batches.append({
                "id": "batch-1",
                "name": "批次 1 (无数据)",
                "status": "pending",
                "progress": 0,
                "totalUrls": 0,
                "completedUrls": 0,
                "failedUrls": 0,
                "startTime": None,
                "endTime": None,
                "urls": []
            })
            total_urls = 0

        # 计算总体进度
        overall_progress = 0
        if total_urls > 0:
            overall_progress = int((completed_urls / total_urls) * 100)

        # 构建任务信息
        task_info = {
            "id": task_id,
            "name": f"爬取任务 - {task_id}",
            "status": "running" if status["status"] == "running" and len(task_manager.running_batches) > 0 else "pending",
            "progress": overall_progress,
            "totalUrls": total_urls,
            "completedUrls": completed_urls,
            "failedUrls": failed_urls,
            "platform": task_detail_data.get("platform", "mercadolibre") if task_detail_data else "mercadolibre",
            "priority": task_detail_data.get("priority", "normal") if task_detail_data else "normal",
            "createdAt": format_local_datetime(task_detail_data.get("created_at")) if task_detail_data else format_local_datetime(),
            "startedAt": None,
            "completedAt": None
        }

        response = TaskDetailResponse(
            success=True,
            task=task_info,
            batches=all_batches
        )

        logger.info(f"Task detail retrieved successfully: {task_id}, {len(all_batches)} batches, {total_urls} total URLs")
        return response

    except Exception as e:
        logger.error(f"Failed to get task detail: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/start")
async def start_task(task_id: str):
    """启动任务"""
    try:
        logger.info(f"Starting task: {task_id}")

        # 从Redis获取任务信息
        import redis.asyncio as redis
        redis_client = redis.from_url("redis://redis:6379/0")

        task_detail_key = f"task_detail:{task_id}"
        task_detail_json = await redis_client.get(task_detail_key)

        if not task_detail_json:
            await redis_client.close()
            raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

        task_detail = json.loads(task_detail_json)

        # 检查任务状态
        if task_detail.get("status") == "running":
            await redis_client.close()
            raise HTTPException(status_code=400, detail="Task is already running")

        # 更新任务状态为运行中
        task_detail["status"] = "running"
        task_detail["updated_at"] = get_local_isoformat()

        # 保存更新后的任务详情
        await redis_client.set(task_detail_key, json.dumps(task_detail))
        await redis_client.close()

        logger.info(f"Task {task_id} started successfully")

        return {
            "success": True,
            "message": f"Task {task_id} started successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/stop")
async def stop_task(task_id: str):
    """停止任务"""
    try:
        logger.info(f"Stopping task: {task_id}")

        # 从Redis获取任务信息
        import redis.asyncio as redis
        redis_client = redis.from_url("redis://redis:6379/0")

        task_detail_key = f"task_detail:{task_id}"
        task_detail_json = await redis_client.get(task_detail_key)

        if not task_detail_json:
            await redis_client.close()
            raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

        task_detail = json.loads(task_detail_json)

        # 检查任务状态
        if task_detail.get("status") != "running":
            await redis_client.close()
            raise HTTPException(status_code=400, detail="Task is not running")

        # 更新任务状态为暂停
        task_detail["status"] = "paused"
        task_detail["updated_at"] = get_local_isoformat()

        # 保存更新后的任务详情
        await redis_client.set(task_detail_key, json.dumps(task_detail))
        await redis_client.close()

        logger.info(f"Task {task_id} stopped successfully")

        return {
            "success": True,
            "message": f"Task {task_id} stopped successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to stop task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{task_id}")
async def delete_task(task_id: str):
    """删除任务（使用统一存储结构）"""
    try:
        logger.info(f"Deleting task: {task_id}")

        # 使用统一的任务存储服务
        from ..services.task_storage_service import get_task_storage
        task_storage = await get_task_storage()

        # 检查任务是否存在
        task_data = await task_storage.get_task(task_id)
        if not task_data:
            # 尝试从旧格式中查找
            import redis.asyncio as redis
            redis_client = redis.from_url("redis://redis:6379/0")
            task_detail_key = f"task_detail:{task_id}"
            task_detail_json = await redis_client.get(task_detail_key)
            await redis_client.close()

            if not task_detail_json:
                raise HTTPException(status_code=404, detail="Task not found")

            # 删除旧格式的任务
            redis_client = redis.from_url("redis://redis:6379/0")
            deleted_count = await redis_client.delete(task_detail_key)
            task_key = f"task:{task_id}"
            await redis_client.delete(task_key)
            await redis_client.close()

            logger.info(f"Deleted legacy task {task_id}")
            return {
                "success": True,
                "message": f"Task {task_id} deleted successfully (legacy format)"
            }

        # 删除新格式的任务
        success = await task_storage.delete_task(task_id)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to delete task")

        logger.info(f"Task deleted successfully: {task_id}")

        return {
            "success": True,
            "message": f"Task {task_id} deleted successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/restart")
async def restart_task_manager():
    """重启任务管理器（用于测试持久化恢复）"""
    global _task_manager
    
    try:
        if _task_manager:
            logger.info("Stopping current TaskManager...")
            await _task_manager.stop()
            _task_manager = None
        
        # 重新初始化
        await get_task_manager()
        
        return {
            "success": True,
            "message": "TaskManager restarted successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to restart TaskManager: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """健康检查"""
    try:
        if _task_manager is None:
            return {"status": "not_initialized", "healthy": False}
        
        # 检查TaskManager状态
        status = _task_manager.get_status()
        persistence_status = _task_manager.get_persistence_status()
        
        healthy = (
            status["status"] == "running" and
            persistence_status["redis_connected"]
        )
        
        return {
            "status": "healthy" if healthy else "unhealthy",
            "healthy": healthy,
            "task_manager_status": status["status"],
            "redis_connected": persistence_status["redis_connected"],
            "persistence_enabled": persistence_status["persistence_enabled"]
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {"status": "error", "healthy": False, "error": str(e)}


@router.get("/timezone")
async def get_server_timezone():
    """获取服务器时区信息"""
    try:
        timezone_info = get_timezone_info()
        return {
            "success": True,
            "data": timezone_info
        }
    except Exception as e:
        logger.error(f"Failed to get timezone info: {e}")
        raise HTTPException(status_code=500, detail=str(e))
