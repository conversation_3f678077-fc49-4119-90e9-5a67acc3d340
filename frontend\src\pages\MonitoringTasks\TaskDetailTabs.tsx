import React from 'react';
import {
  Card,
  Table,
  Tag,
  Timeline,
  Row,
  Col,
  Statistic,
  Progress,
  Empty,
  Tooltip,
  Button,
  Space
} from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  PlayCircleOutlined,
  StopOutlined,
  BugOutlined,
  ReloadOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import { useTaskExecutionState } from '../../hooks/useTaskExecutionState';
import type { ColumnsType } from 'antd/es/table';

interface ExecutionHistory {
  id: string;
  start_time: string;
  end_time?: string;
  status: string;
  trigger: string;
  url_count: number;
  success_count: number;
  error_count: number;
  duration: number;
  error_message?: string;
}

interface TaskStats {
  url_stats: {
    total_urls: number;
    active_urls: number;
    disabled_urls: number;
    error_urls: number;
  };
  execution_stats: {
    total_executions: number;
    successful_executions: number;
    failed_executions: number;
    success_rate: number;
    avg_duration: number;
  };
  task_info: {
    created_at: string;
    last_run?: string;
    next_run?: string;
    status: string;
    is_running: boolean;
  };
}

// 执行历史Tab组件
export const ExecutionHistoryTab: React.FC<{ 
  executionHistory: ExecutionHistory[]; 
  onRefresh: () => void; 
}> = ({ executionHistory, onRefresh }) => {
  const historyColumns: ColumnsType<ExecutionHistory> = [
    {
      title: '执行ID',
      dataIndex: 'id',
      key: 'id',
      width: 200,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusConfig = {
          running: { color: 'blue', icon: <ClockCircleOutlined />, text: '执行中' },
          success: { color: 'green', icon: <CheckCircleOutlined />, text: '成功' },
          failed: { color: 'red', icon: <CloseCircleOutlined />, text: '失败' },
          terminated: { color: 'orange', icon: <StopOutlined />, text: '已终止' },
        };
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.failed;
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: '触发方式',
      dataIndex: 'trigger',
      key: 'trigger',
      width: 100,
      render: (trigger: string) => (
        <Tag color={trigger === 'manual' ? 'blue' : 'green'}>
          {trigger === 'manual' ? '手动' : '自动'}
        </Tag>
      ),
    },
    {
      title: 'URL数量',
      dataIndex: 'url_count',
      key: 'url_count',
      width: 100,
    },
    {
      title: '成功/失败',
      key: 'success_rate',
      width: 120,
      render: (_, record) => (
        <span>
          <span style={{ color: '#3f8600' }}>{record.success_count}</span>
          {' / '}
          <span style={{ color: '#cf1322' }}>{record.error_count}</span>
        </span>
      ),
    },
    {
      title: '执行时长',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: (duration: number) => `${duration}s`,
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: 'start_time',
      width: 180,
    },
    {
      title: '结束时间',
      dataIndex: 'end_time',
      key: 'end_time',
      width: 180,
      render: (time: string) => time || '未结束',
    },
    {
      title: '错误信息',
      dataIndex: 'error_message',
      key: 'error_message',
      ellipsis: true,
      render: (message: string) => message ? (
        <Tooltip title={message}>
          <span style={{ color: '#cf1322' }}>{message}</span>
        </Tooltip>
      ) : '-',
    },
  ];

  if (executionHistory.length === 0) {
    return (
      <Card title="执行历史">
        <Empty description="暂无执行历史" />
      </Card>
    );
  }

  return (
    <Card title="执行历史">
      <Table
        columns={historyColumns}
        dataSource={executionHistory}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条执行记录`,
        }}
        scroll={{ x: 1200 }}
      />
    </Card>
  );
};

// 统计图表Tab组件
export const StatsTab: React.FC<{ 
  taskStats: TaskStats | null; 
}> = ({ taskStats }) => {
  if (!taskStats) {
    return (
      <Card title="统计信息">
        <Empty description="暂无统计数据" />
      </Card>
    );
  }

  const { url_stats, execution_stats } = taskStats;

  return (
    <div>
      <Row gutter={[24, 24]}>
        {/* URL统计 */}
        <Col span={12}>
          <Card title="URL统计">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic 
                  title="总URL数" 
                  value={url_stats.total_urls} 
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="活跃URL" 
                  value={url_stats.active_urls} 
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="禁用URL" 
                  value={url_stats.disabled_urls} 
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="错误URL" 
                  value={url_stats.error_urls} 
                  valueStyle={{ color: '#cf1322' }}
                />
              </Col>
            </Row>
            
            <div style={{ marginTop: 24 }}>
              <div style={{ marginBottom: 8 }}>URL状态分布</div>
              <Progress 
                percent={url_stats.total_urls > 0 ? (url_stats.active_urls / url_stats.total_urls * 100) : 0}
                success={{ 
                  percent: url_stats.total_urls > 0 ? (url_stats.active_urls / url_stats.total_urls * 100) : 0 
                }}
                format={() => `${url_stats.active_urls}/${url_stats.total_urls}`}
              />
            </div>
          </Card>
        </Col>

        {/* 执行统计 */}
        <Col span={12}>
          <Card title="执行统计">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic 
                  title="总执行次数" 
                  value={execution_stats.total_executions} 
                  prefix={<PlayCircleOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="成功次数" 
                  value={execution_stats.successful_executions} 
                  valueStyle={{ color: '#3f8600' }}
                  prefix={<CheckCircleOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="失败次数" 
                  value={execution_stats.failed_executions} 
                  valueStyle={{ color: '#cf1322' }}
                  prefix={<CloseCircleOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="平均时长" 
                  value={execution_stats.avg_duration} 
                  suffix="s"
                  precision={1}
                />
              </Col>
            </Row>
            
            <div style={{ marginTop: 24 }}>
              <div style={{ marginBottom: 8 }}>成功率</div>
              <Progress 
                percent={execution_stats.success_rate}
                status={execution_stats.success_rate > 80 ? 'success' : execution_stats.success_rate > 60 ? 'normal' : 'exception'}
                format={(percent) => `${percent}%`}
              />
            </div>
          </Card>
        </Col>

        {/* 执行趋势时间线 */}
        <Col span={24}>
          <Card title="最近执行记录">
            <Timeline
              items={[
                {
                  color: "green",
                  dot: <CheckCircleOutlined />,
                  children: (
                    <div>
                      <strong>任务创建</strong>
                      <div style={{ color: '#666', fontSize: '12px' }}>
                        {taskStats.task_info.created_at}
                      </div>
                    </div>
                  )
                },
                ...(taskStats.task_info.last_run ? [{
                  color: "blue" as const,
                  dot: <PlayCircleOutlined />,
                  children: (
                    <div>
                      <strong>最后执行</strong>
                      <div style={{ color: '#666', fontSize: '12px' }}>
                        {taskStats.task_info.last_run}
                      </div>
                    </div>
                  )
                }] : []),
                ...(taskStats.task_info.next_run ? [{
                  color: "orange" as const,
                  dot: <ClockCircleOutlined />,
                  children: (
                    <div>
                      <strong>下次执行</strong>
                      <div style={{ color: '#666', fontSize: '12px' }}>
                        {taskStats.task_info.next_run}
                      </div>
                    </div>
                  )
                }] : [])
              ]}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

// 执行状态Tab组件
export const ExecutionStatusTab: React.FC<{
  taskId: string;
}> = ({ taskId }) => {
  const {
    state,
    loading,
    error,
    refresh,
    urlStatuses,
    summary,
    lastUpdate
  } = useTaskExecutionState({
    taskId,
    autoConnect: true,
    pollInterval: 3000,
    onUrlStatusChange: (url, status) => {
      console.log(`URL ${url} status changed to ${status.status}`);
    },
    onTaskProgress: (progress) => {
      console.log(`Task progress: ${progress.percentage}%`);
    }
  });

  // URL状态表格列定义
  const urlColumns = [
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      ellipsis: true,
      render: (url: string) => (
        <Tooltip title={url}>
          <span style={{ fontSize: '12px', fontFamily: 'monospace' }}>
            {url.length > 60 ? `${url.substring(0, 60)}...` : url}
          </span>
        </Tooltip>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => {
        const statusConfig = {
          pending: { color: 'default', icon: <ClockCircleOutlined />, text: '等待中' },
          processing: { color: 'processing', icon: <LoadingOutlined />, text: '处理中' },
          completed: { color: 'success', icon: <CheckCircleOutlined />, text: '已完成' },
          failed: { color: 'error', icon: <ExclamationCircleOutlined />, text: '失败' }
        };
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      }
    },
    {
      title: 'Worker',
      dataIndex: 'workerId',
      key: 'workerId',
      width: 120,
      render: (workerId: string) => workerId ? <Tag color="blue">{workerId}</Tag> : '-'
    },
    {
      title: '响应时间',
      dataIndex: 'responseTime',
      key: 'responseTime',
      width: 100,
      render: (time: number) => time ? `${time.toFixed(2)}s` : '-'
    },
    {
      title: '开始时间',
      dataIndex: 'startedAt',
      key: 'startedAt',
      width: 160,
      render: (time: string) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '完成时间',
      dataIndex: 'completedAt',
      key: 'completedAt',
      width: 160,
      render: (time: string) => time ? new Date(time).toLocaleString() : '-'
    }
  ];

  // 获取状态统计
  const getStatusStats = () => {
    return [
      { label: '总计', value: summary.total, color: '#1890ff' },
      { label: '等待中', value: summary.pending, color: '#faad14' },
      { label: '处理中', value: summary.processing, color: '#52c41a' },
      { label: '已完成', value: summary.completed, color: '#52c41a' },
      { label: '失败', value: summary.failed, color: '#ff4d4f' }
    ];
  };

  // 计算进度百分比
  const getProgressPercentage = () => {
    if (summary.total === 0) return 0;
    return Math.round(((summary.completed + summary.failed) / summary.total) * 100);
  };

  if (error) {
    return (
      <Card title="URL执行状态">
        <Empty
          description={
            <div>
              <div>加载执行状态失败</div>
              <div style={{ color: '#ff4d4f', fontSize: '12px', marginTop: 8 }}>
                {error}
              </div>
            </div>
          }
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button type="primary" onClick={refresh}>
            重试
          </Button>
        </Empty>
      </Card>
    );
  }

  return (
    <div>
      {/* 页面头部 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Space>
            <span style={{ fontSize: '16px', fontWeight: 'bold' }}>URL执行状态</span>
            <Tag color="blue">🔧 演示功能</Tag>
          </Space>
          <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
            最后更新: {new Date(lastUpdate).toLocaleString()}
          </div>
        </Col>
        <Col>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={refresh}
              loading={loading}
              size="small"
            >
              刷新
            </Button>
            <Button
              icon={<BugOutlined />}
              onClick={() => window.open(`/crawler-debug?task_id=${taskId}`, '_blank')}
              size="small"
            >
              调试工具
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 统计概览 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={12}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: 8 }}>执行进度</div>
              <Progress
                type="circle"
                percent={getProgressPercentage()}
                status={summary.failed > 0 ? 'exception' : 'active'}
                size={80}
                format={(percent) => `${percent}%`}
              />
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card size="small">
            <Row gutter={8}>
              {getStatusStats().map((stat, index) => (
                <Col span={4} key={index}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '18px', fontWeight: 'bold', color: stat.color }}>
                      {stat.value}
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      {stat.label}
                    </div>
                  </div>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      </Row>

      {/* URL执行状态表格 */}
      <Card title="URL详细状态" size="small">
        {Object.keys(urlStatuses).length === 0 ? (
          <Empty
            description={
              <div>
                <div>暂无URL执行状态数据</div>
                <div style={{ fontSize: '12px', color: '#666', marginTop: 8 }}>
                  🔧 此功能需要后端API支持，当前显示为演示状态
                </div>
              </div>
            }
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <Table
            columns={urlColumns}
            dataSource={Object.values(urlStatuses)}
            rowKey="url"
            loading={loading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }}
            scroll={{ x: 800 }}
            size="small"
          />
        )}
      </Card>
    </div>
  );
};
