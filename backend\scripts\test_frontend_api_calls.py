#!/usr/bin/env python3
"""
测试前端API调用

模拟前端的实际请求，验证所有API端点
"""

import requests
import json

def test_all_apis():
    """测试所有API端点"""
    base_url = "http://localhost:8000"
    
    print("🧪 测试前端API调用")
    print("=" * 50)
    
    # 测试1: 健康检查
    print("\n📋 测试1: 健康检查")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        print(f"  状态码: {response.status_code}")
        if response.status_code == 200:
            print("  ✅ 健康检查通过")
        else:
            print("  ❌ 健康检查失败")
    except Exception as e:
        print(f"  ❌ 异常: {e}")
    
    # 测试2: 获取监控任务列表
    print("\n📋 测试2: 获取监控任务列表")
    try:
        params = {
            "sort_by": "created_at",
            "sort_order": "desc",
            "page": 1,
            "page_size": 20
        }
        response = requests.get(f"{base_url}/api/v1/monitoring-tasks/", params=params, timeout=10)
        print(f"  状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("  ✅ 任务列表获取成功")
            print(f"  任务总数: {data.get('total', 0)}")
        else:
            print("  ❌ 任务列表获取失败")
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"  ❌ 异常: {e}")
    
    # 测试3: 任务验证API
    print("\n📋 测试3: 任务验证API")
    try:
        validate_data = {
            "name": "前端测试任务",
            "url_ids": ["url_123", "url_456"],
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "09:00",
                "timezone": "Asia/Shanghai"
            }
        }
        
        response = requests.post(f"{base_url}/api/v1/tasks/validate", json=validate_data, timeout=10)
        print(f"  状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("  ✅ 任务验证成功")
            print(f"  验证结果: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print("  ❌ 任务验证失败")
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"  ❌ 异常: {e}")
    
    # 测试4: URL池预览API
    print("\n📋 测试4: URL池预览API")
    try:
        preview_data = {
            "url_ids": ["test_url_1", "test_url_2"]
        }
        
        response = requests.post(f"{base_url}/api/v1/tasks/preview-urls", json=preview_data, timeout=10)
        print(f"  状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("  ✅ URL预览成功")
            print(f"  预览结果: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print("  ❌ URL预览失败")
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"  ❌ 异常: {e}")
    
    # 测试5: 创建任务API
    print("\n📋 测试5: 创建任务API")
    try:
        create_data = {
            "name": "API测试任务",
            "description": "通过API创建的测试任务",
            "schedule": {
                "type": "daily",
                "enabled": True,
                "time": "09:00",
                "timezone": "Asia/Shanghai"
            },
            "config": {
                "platform": "mercadolibre",
                "batch_size": 10,
                "max_retries": 3,
                "timeout": 300,
                "priority": "medium"
            },
            "urls": [
                "https://example.com/product/1",
                "https://example.com/product/2"
            ]
        }
        
        response = requests.post(f"{base_url}/api/v1/monitoring-tasks/", json=create_data, timeout=10)
        print(f"  状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("  ✅ 任务创建成功")
            print(f"  任务ID: {data.get('data', {}).get('id', 'N/A')}")
            
            # 清理测试任务
            task_id = data.get('data', {}).get('id')
            if task_id:
                delete_response = requests.delete(f"{base_url}/api/v1/monitoring-tasks/{task_id}", timeout=10)
                if delete_response.status_code in [200, 204]:
                    print("  ✅ 测试任务已清理")
                else:
                    print("  ⚠️ 测试任务清理失败")
        else:
            print("  ❌ 任务创建失败")
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"  ❌ 异常: {e}")
    
    # 测试6: URL池相关API
    print("\n📋 测试6: URL池API")
    try:
        response = requests.get(f"{base_url}/api/v1/url-pool/", timeout=10)
        print(f"  状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("  ✅ URL池获取成功")
            print(f"  URL总数: {data.get('total', 0)}")
        else:
            print("  ❌ URL池获取失败")
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"  ❌ 异常: {e}")
    
    print("\n🎉 所有API测试完成!")

if __name__ == "__main__":
    test_all_apis()
